{
    "name" : "囊中对比",
    "appid" : "__UNI__F16D5DD",
    "description" : "搜集各区域不同版本申论答案，进行在线查询对比",
    "versionName" : "1.0.8",
    "versionCode" : 136,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "screenOrientation" : [
            "portrait-primary",
            "portrait-secondary",
            "landscape-primary",
            "landscape-secondary"
        ],
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "OAuth" : {},
            "Payment" : {},
            "Share" : {},
            "Camera" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:srnz.net" ]
                    }
                },
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "是否允许访问相册，以便上传照片和动态？",
                    "NSLocationWhenInUseUsageDescription" : "是否允许访问位置，以便显示当前位置信息或查找附近数据？",
                    "NSLocationAlwaysUsageDescription" : "是否允许访问位置，以便显示当前位置信息或查找附近数据？",
                    "NSCameraUsageDescription" : "是否允许访问相机，以便上传照片和动态？"
                },
                "dSYMs" : false,
                "idfa" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxb5e67df0de487557",
                        "appsecret" : "cf42129310db580c64edc0bc21a35e13",
                        "UniversalLinks" : "https://srnz.net/apple-app-site-association/"
                    },
                    "apple" : {}
                },
                "payment" : {
                    "appleiap" : {},
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxb5e67df0de487557",
                        "UniversalLinks" : "https://srnz.net/apple-app-site-association/"
                    }
                },
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxb5e67df0de487557",
                        "appsecret" : "cf42129310db580c64edc0bc21a35e13",
                        "UniversalLinks" : "https://srnz.net/apple-app-site-association/"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true,
                "androidStyle" : "common"
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "embeddedAppIdList" : [ "wxab6d9433a7ee3945" ],
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "_spaceID" : "mp-256ec868-1269-4317-95f9-7235242f9dc3"
}
