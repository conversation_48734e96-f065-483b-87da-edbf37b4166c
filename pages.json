{
	"pages": [{
			"path": "pages/init/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mime/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/answer/index",
			"style": {
				"navigationBarTitleText": "答案记录",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/answer-add/index",
			"style": {
				"navigationBarTitleText": "补充答案",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/answer-history/index",
			"style": {
				"navigationBarTitleText": "历史答案",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/area/index",
			"style": {
				"navigationBarTitleText": "一键搜题",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/article/index",
			"style": {
				"navigationBarTitleText": "文章精选",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/auth/index",
			"style": {
				"navigationBarTitleText": "认证授权",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/detail/index",
			"style": {
				"navigationBarTitleText": "试卷详情",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/info/index",
			"style": {
				"navigationBarTitleText": "信息修改",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/login/register",
			"style": {
				"navigationBarTitleText": "注册新账号",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/login/forget",
			"style": {
				"navigationBarTitleText": "找回密码",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/note/index",
			"style": {
				"navigationBarTitleText": "随手记",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/ocr/index",
			"style": {
				"navigationBarTitleText": "在线识别",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/paper/index",
			"style": {
				"navigationBarTitleText": "试卷",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/paper-collect/index",
			"style": {
				"navigationBarTitleText": "收藏夹",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/pay-result/index",
			"style": {
				"navigationBarTitleText": "支付结果",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/service/agreement",
			"style": {
				"navigationBarTitleText": "服务协议",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/service/privicy",
			"style": {
				"navigationBarTitleText": "隐私政策",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/setting/index",
			"style": {
				"navigationBarTitleText": "系统设置",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/suggest/index",
			"style": {
				"navigationBarTitleText": "软件优化建议",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/ai/index",
			"style": {
				"navigationBarTitleText": "ai",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/zt/index",
			"style": {
				"navigationBarTitleText": "真题字帖",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/version/index",
			"style": {
				"navigationBarTitleText": "会员升级",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "pages/version-activity/index",
			"style": {
				"navigationBarTitleText": "免费领会员",
				"navigationBarBackgroundColor": "#F7FAF9"
			}
		},
		{
			"path": "uni_modules/uni-upgrade-center-app/pages/upgrade-popup",
			"style": {
				"disableScroll": true,
				"app-plus": {
					"backgroundColorTop": "transparent",
					"background": "transparent",
					"titleNView": false,
					"scrollIndicator": false,
					"popGesture": "none",
					"animationType": "fade-in",
					"animationDuration": 200

				}
			}
		}
	],
	"easycom": {
		"autoscan": true,
		"custom": {
			"fui-(.*)": "@/components/firstui/fui-$1/fui-$1.vue",
			// uni-ui 规则如下配置
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			"^uv-(.*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue"
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#FFF",
		"backgroundColor": "#FFF",
		"backgroundColorBottom": "#FFF",
		"app-plus": {
			"bounce": "none",
			"screenOrientation": [
				"portrait-primary", //可选，字符串类型，支持竖屏  
				"portrait-secondary", //可选，字符串类型，支持反向竖屏  
				"landscape-primary", //可选，字符串类型，支持横屏  
				"landscape-secondary" //可选，字符串类型，支持反向横屏  
			]
		},
		"rpxCalcMaxDeviceWidth": 750, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		"rpxCalcBaseDeviceWidth": 375, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
		"rpxCalcIncludeWidth": 750, // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750，
		"pageOrientation": "auto"
	},

	"tabBar": {
		"backgroundColor": "#FFF",
		"selectedColor": "#46B4B1",

		"list": [{
				"pagePath": "pages/home/<USER>",
				"text": "学习",
				"iconPath": "static/images/home-unselect-tab-icon.png",
				"selectedIconPath": "static/images/home-tab-icon.png"

			},
			// {
			// 	"pagePath": "pages/community/index",
			// 	"text": "社区",
			// 	"iconPath": "static/images/community-unselect-tab-icon.png",
			// 	"selectedIconPath": "static/images/community-tab-icon.png"
			// },
			{
				"pagePath": "pages/mime/index",
				"text": "我的",
				"iconPath": "static/images/user-unselect-tab-icon.png",
				"selectedIconPath": "static/images/user-tab-icon.png"
			}
		]
	},
	"uniIdRouter": {}
}