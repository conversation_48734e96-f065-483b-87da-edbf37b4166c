import App from './App'
import Load from './components/load'
import RichText from './components/richText'
import Empty from './components/empty'
import DefineDialog from './components/dialog'
import * as aesUtil from '@/utils/aes/encryption-utils.js'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
Vue.component('load', Load);
Vue.component('richText', RichText);
Vue.component('empty', Empty);
Vue.component('DefineDialog', DefineDialog);
Vue.prototype.$aesUtil = aesUtil;
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  app.component('load', Load);
  app.component('richText', RichText);
  app.component('empty', Empty);
  app.component('DefineDialog', DefineDialog);
  app.config.globalProperties.$aesUtil = aesUtil;
  return {
    app
  }
}
// #endif