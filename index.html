<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
	<style>
		input, textarea {
		    -webkit-user-select: auto!important;
		    -khtml-user-select: auto!important;
		    -moz-user-select: auto!important;
		    -ms-user-select: auto!important;
		    -o-user-select: auto!important;
		    user-select: auto!important;
		}
	</style>
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
	<script src="@/utils/aes/aes-min.js"></script>
	<script src="@/utils/aes/cipher-core-min.js"></script>
	<script src="@/utils/aes/core-min.js"></script>
	<script src="@/utils/aes/mode-ecb.js"></script>
    <title></title>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/main.js"></script>
  </body>
</html>
