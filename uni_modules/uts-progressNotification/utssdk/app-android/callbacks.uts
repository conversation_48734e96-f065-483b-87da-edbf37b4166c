let globalNotificationProgressCallBack : (() => void) | null = () => { }
let globalNotificationProgressFinishCallBack : (() => void) | null = () => { }

export function setGlobalNotificationProgressCallBack(callBack : (() => void) | null) : void {
  globalNotificationProgressCallBack = callBack
}

export function getGlobalNotificationProgressCallBack() : (() => void) | null {
  return globalNotificationProgressCallBack
}


export function setGlobalNotificationProgressFinishCallBack(callBack : (() => void) | null) : void {
  globalNotificationProgressFinishCallBack = callBack
}

export function getGlobalNotificationProgressFinishCallBack() : (() => void) | null {
  return globalNotificationProgressFinishCallBack
}
