import { get, post, put, del } from '@/utils/request'
export const uploadMinioUrl = "https://srnz.net:8701/common/uploadMinio";
export const updateScanCodeStatus = (data : any) : Promise<any> => post(`/nz/user/updateScanCodeStatus`, data);
export const login = (data : any) : Promise<any> => post(`/nz/login`, data);
export const register = (data : any) : Promise<any> => post(`/nz/user/register`, data);
export const unregister = (data : any) : Promise<any> => get(`/nz/user/unregister`, data);
export const resetPassword = (data : any) : Promise<any> => put(`/nz/user/resetPassword`, data);
export const getUserInfo = (data : any) : Promise<any> => get(`/nz/getUserInfo`, data);
export const sendEmailCode = (data : any) : Promise<any> => get(`/nz/sendEmailCode`, data);
export const getOcr = (data : any) : Promise<any> => get(`/common/ocr`, data);
export const getAes = (data : any) : Promise<any> => get(`/common/aes`, data);
export const getAreaList = (data : any) : Promise<any> => get(`/nz/area/list`, data);
export const getPaperList = (data : any) : Promise<any> => get(`/nz/paper/list`, data);
export const getQuestionList = (data : any) : Promise<any> => get(`/nz/question/list`, data);
export const getAnswerList = (data : any) : Promise<any> => get(`/nz/answer/list`, data);
export const getMaterialList = (data : any) : Promise<any> => get(`/nz/material/list`, data);
export const getUserAnswerList = (data : any) : Promise<any> => get(`/nz/userAnswer/list`, data);
export const getCollectPaperList = (data : any) : Promise<any> => get(`/nz/paper/collectList`, data);
export const getUserAddAnswerList = (data : any) : Promise<any> => get(`/nz/userAddAnswer/list`, data);
export const getUserCommentList = (data : any) : Promise<any> => get(`/nz/userComment/list`, data);
export const getUserNoteList = (data : any) : Promise<any> => get(`/nz/userNote/list`, data);
export const getTargetsActionStatus = (data : any) : Promise<any> => get(`/nz/userAction/getTargetsActionStatus`, data);
export const doAction = (data : any) : Promise<any> => post(`/nz/userAction`, data);
export const getNoticeList = (data : any) : Promise<any> => get(`/nz/notice/list`, data);
export const getVersionMealList = (data : any) : Promise<any> => get(`/nz/versionMeal/list`, data);
export const userActivityPermission = (data : any) : Promise<any> => get(`/nz/userVersion/userActivityPermission`, data);
export const insertUserAnswer = (data : any) : Promise<any> => post(`/nz/userAnswer`, data);
export const insertUserAddAnswer = (data : any) : Promise<any> => post(`/nz/userAddAnswer`, data);
export const insertUserComment = (data : any) : Promise<any> => post(`/nz/userComment`, data);
export const insertUserNote = (data : any) : Promise<any> => post(`/nz/userNote`, data);
export const insertUserVersion = (data : any) : Promise<any> => post(`/nz/userVersion`, data);
export const insertSuggest = (data : any) : Promise<any> => post(`/nz/suggest`, data);
export const updateUser = (data : any) : Promise<any> => put(`/nz/user`, data);
export const deleteUserAnswer = (ids : any) : Promise<any> => del(`/nz/userAnswer/${ids}`);
export const deleteUserComment = (ids : any) : Promise<any> => del(`/nz/userComment/${ids}`);
export const deleteUserNote = (ids : any) : Promise<any> => del(`/nz/userNote/${ids}`);
export const createOrder = (data : any) : Promise<any> => post(`/nz/userPayOrder/create`, data);
export const activePay = (data : any) : Promise<any> => post(`/pay/activePay`, data);
export const appPayConfig = (data : any) : Promise<any> => get(`/pay/appPayConfig`, data);
export const validateApplePayment = (data : any) : Promise<any> => post(`/pay/validateApplePayment`, data);