<template>
	<view class="context">
		<view class="wrapper">
			<view class="wrapper-content">
				<view class="header" :style="showIcon ? 'height:210rpx;': 'height:120rpx;'">
					<image src="/static/images/dialog-icon.png" v-show="showIcon"></image>
				</view>
				<view class="body">
					<!-- 如果有插槽内容，则使用插槽；否则，显示 content 属性 -->
					<slot v-if="$slots.default"></slot>
					<view v-else>{{ content }}</view>
				</view>
				<view class="footer">
					<view class="cancel" @click="cancel">取消</view>
					<view class="confirm" @click="confirm">确认</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			showIcon: {
				type: Boolean,
				default: true
			},
			content: {
				type: String,
				default: ""
			}
		},
		data() {
			return {}
		},
		methods: {
			cancel() {
				this.$emit('cancel', {});
			},
			confirm() {
				this.$emit('confirm', {});
			}
		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss" scoped>
	.context {
		height: 120%;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.3);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;
		top: -150rpx;
	}
	
	.context .wrapper {
		width: 540rpx;
		flex-shrink: 0;
		border-radius: 24rpx;
		background: $uni-bg-color;;
		margin: 0rpx;
		padding: 0rpx;
		position: relative;
	}
	
	.context .wrapper .wrapper-content {
		width: 100%;
		height: 100%;
		position: relative;
		top: -90rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.context .wrapper .wrapper-content .header {
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 600;
		width: 100%;
	}
	
	.context .wrapper .wrapper-content .header image {
		height: 210rpx;
	}
	
	.context .wrapper .wrapper-content .body {
		height: 100%;
		position: relative;
		padding: 0 24rpx;
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}
	
	.context .wrapper .wrapper-content .footer {
		display: flex;
		position: relative;
		justify-content: flex-end;
		width: 100%;
		bottom: -90rpx;
	}
	
	.context .wrapper .wrapper-content .cancel {
		width: 50%;
		margin: 0rpx;
		padding: 24rpx 0;
		border: 1px solid $uni-text-color-disable;
		text-align: center;
		border-bottom: none;
		border-left: none;
		color: $uni-text-color-grey;
	}
	
	.context .wrapper .wrapper-content .confirm {
		width: 50%;
		margin: 0rpx;
		padding: 24rpx 0;
		border-top: 1px solid $uni-text-color-disable;
		text-align: center;
		color: $uni-color-primary;
	}
</style>