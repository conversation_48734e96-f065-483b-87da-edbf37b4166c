<template>
	<view class="load-bg" :style="'top:'+loaddingTop + ';'">
		<view class="load">
			<view class="load-box">
				<image class="load-box-img" src="@/static/images/loading.gif"></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			loadinTime: {
				type: Number,
				value: 0
			},
			loaddingTop: {
				type: Number | String,
				value: 0
			}
		},
		data() {
			return {

			}
		},
		methods: {

		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss" scoped>
	.load-bg {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 9999;
	}

	.load {
		position: fixed;
		top: 20%;
		margin-top: -40rpx;
		width: 100%;
	}

	.load-box {
		height: 80rpx;
		text-align: center;
	}

	.load-box .load-box-img {
		width: 80rpx;
		height: 80rpx;
	}
</style>