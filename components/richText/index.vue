<template>
	<view class="whole" id="richText">
		<view class="editor-toolbar">
			<view class="toolbar-2">
				<view class="tool-item-cell">
					<view class="tool-item-box">
						<scroll-view scroll-x class="flex-sb" :show="false">
							<!-- <view class="tool-item">
							  <i class="iconfont icon-charutupian" @click="toolEvent('insertImage')"></i>
							</view> -->
							<view class="tool-item">
								<i class="iconfont icon-zitijiacu" :class="{ 'ql-active': formats.bold }"
									@click="format('bold')"></i>
							</view>
							<view class="tool-item">
								<i class="iconfont icon-text_color"
									:class="{ 'ql-active-1': formats.color === '#f73131' }"
									@click="format('color', '#f73131')"></i>
							</view>
							<view class="tool-item">
								<i class="iconfont icon-zitixiahuaxian" :class="{ 'ql-active': formats.underline }"
									@click="format('underline')"></i>
							</view>
							<view class="tool-item">
								<i class="iconfont icon-zuoduiqi" :class="{ 'ql-active': formats.align === 'left' }"
									@click="format('align','left')"></i>
							</view>
							<view class="tool-item">
								<i class="iconfont icon-juzhongduiqi"
									:class="{ 'ql-active': formats.align === 'center' }"
									@click="format('align','center')"></i>
							</view>
							<view class="tool-item">
								<i class="iconfont icon-youduiqi" :class="{ 'ql-active': formats.align === 'right' }"
									@click="format('align','right')"></i>
							</view>
							<view class="tool-item">
								<image src="@/static/images/ocr.png" @click="toolEvent('ocrScan')" />
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>
		<editor :id="dynamicID" class="ql-container" placeholder="开始输入..." show-img-size show-img-toolbar show-img-resize
			@statuschange="onStatusChange" :read-only="readOnly" @ready="onEditorReady" @focus="bindfocus"
			@blur="bindblur" @input="bindinput">
		</editor>
	</view>
</template>

<script>
	const supportDateFormat = ['YY-MM', 'YY.MM.DD', 'YY-MM-DD', 'YY/MM/DD', 'YY.MM.DD HH:MM', 'YY/MM/DD HH:MM',
		'YY-MM-DD HH:MM'
	];

	export default {
		props: {
			readOnly: {
				type: Boolean,
				default: false
			},
			placeholder: {
				type: String,
				default: '开始输入...'
			},
			formatDate: {
				type: String,
				default: 'YY/MM/DD'
			},
			buttonTxt: {
				type: String,
				default: '保存'
			}
		},
		data() {
			return {
				formats: {},
				textTool: false,
				dynamicID: 'editor' + new Date().getMilliseconds() + ''
			}
		},
		methods: {
			toolEvent(toolName) {
				switch (toolName) {
					case 'insertImage': //插入图片
						this.insertImageEvent();
						break;
					case 'showTextTool': //展示文字编辑工具
						this.showTextTool();
						break;
					case 'insertDate': //插入日期
						this.insertDate();
						break;
					case 'undo': //撤退（向前）
						this.undo();
						break;
					case 'redo': //撤退（向后）
						this.restore();
						break;
					case 'clear': //清除
						this.clearBeforeEvent();
						break;
					case 'ocrScan':
						this.ocrScanEvent();
						break;
				}
			},

			showTextTool() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				})
				this.textTool = !this.textTool;
			},

			insertDate() {
				if (supportDateFormat.indexOf(this.formatDate) < 0) {
					console.error(
						`Format Date ${this.formatDate} error \n It should be one of them [${supportDateFormat}]`)
					return;
				}
				let formatDate = this.getThisDate(this.formatDate);
				this.editorCtx.insertText({
					text: formatDate
				})
			},


			insertImageEvent() {
				this.$emit('insertImageEvent', {});
			},

			ocrScanEvent() {
				this.$emit('ocrScanEvent', {});
			},

			insertImageMethod(path) {
				return new Promise((resolve, reject) => {
					this.editorCtx.insertImage({
						src: path,
						data: {
							id: 'imgage',
						},
						success: res => {
							resolve(res);
						},
						fail: res => {
							reject(res);
						}
					})
				})
			},

			getEditorContent() {
				this.editorCtx.getContents({
					success: res => {
						this.$emit('getEditorContent', {
							value: res,
						});
					}
				})
			},

			undo() {
				this.editorCtx.undo();
				this.$emit('undo');
			},

			restore() {
				this.editorCtx.redo();
				this.$emit('restore');
			},

			format(name, value) {
				if (!name) return;
				this.editorCtx.format(name, value);
			},

			insertDivider() {
				this.editorCtx.insertDivider({
					success: res => {}
				})
			},

			clear() {
				this.editorCtx.clear({
					success: res => {
						this.$emit('clearSuccess');
					}
				})
			},

			clearBeforeEvent() {
				this.$emit('clearBeforeEvent');
			},

			removeFormat() {
				this.editorCtx.removeFormat();
			},

			onStatusChange(res) {
				this.formats = res.detail;
			},

			onEditorReady() {
				this.$emit('onEditorReady');
				uni.createSelectorQuery().select('#' + this.dynamicID).context(res => {
					this.editorCtx = res.context;
					let rtTxt = '';
					this.setContents(rtTxt); //设置富文本内容
				}).exec();
			},

			//设置富文本内容
			setContents(rechtext) {
				this.editorCtx.setContents({
					html: rechtext,
					success: res => {}
				})
			},

			bindfocus(res) {
				this.$emit('bindfocus', {
					value: res,
				});
			},

			bindblur(res) {
				this.$emit('bindblur', {
					value: res,
				});
			},

			bindinput(res) {
				this.$emit('bindinput', {
					value: res,
				});
			},

			getThisDate(format) {
				let date = new Date(),
					year = date.getFullYear(),
					month = date.getMonth() + 1,
					day = date.getDate(),
					h = date.getHours(),
					m = date.getMinutes();

				const zero = (value) => {
					if (value < 10) return '0' + value;
					return value;
				}

				switch (format) {
					case 'YY-MM':
						return year + '-' + zero(month);
					case 'YY.MM.DD':
						return year + '.' + zero(month) + '.' + zero(day);
					case 'YY-MM-DD':
						return year + '-' + zero(month) + '-' + zero(day);
					case 'YY.MM.DD HH:MM':
						return year + '.' + zero(month) + '.' + zero(day) + ' ' + zero(h) + ':' + zero(m);
					case 'YY/MM/DD HH:MM':
						return year + '/' + zero(month) + '/' + zero(day) + ' ' + zero(h) + ':' + zero(m);
					case 'YY-MM-DD HH:MM':
						return year + '-' + zero(month) + '-' + zero(day) + ' ' + zero(h) + ':' + zero(m);
					default:
						return year + '/' + zero(month) + '/' + zero(day);
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import "@/static/css/iconfont.scss";

	.editor-toolbar i {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.editor-toolbar .tool-item {
		display: inline-block;
	}

	.flex-sb {
		height: 70rpx;
	}

	.toolbar-2 {
		position: fixed;
		padding: 5rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
	}

	.toolbar-2 .tool-item-box {
		position: relative;
	}

	.tool-item image {
		display: flex;
		padding: 8px;
		width: 20px;
		height: 20px;
		cursor: pointer;
		font-size: 20px;
		justify-content: center;
		align-items: center;
		position: relative;
		top: 4rpx;
	}

	.iconfont {
		display: inline-block;
		padding: 8px;
		width: 20px;
		height: 20px;
		cursor: pointer;
		font-size: 20px;
	}

	.ql-container {
		box-sizing: border-box;
		min-height: 40vh;
		max-height: 65vh;
		font-size: 28rpx;
		line-height: 1.5;
		height: auto;
		overflow-y: scroll;
	}

	.ql-container::-webkit-scrollbar {
		display: none;
	}

	.ql-active {
		color: #46B4B1;
	}

	.ql-active-1 {
		color: #f73131;
	}
</style>