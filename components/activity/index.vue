<template>
	<view class="context">
		<view class="wrapper">
			<view class="wrapper-content">
				<image src="/static/images/activity.png"></image>
				<view class="confirm" @click="confirm()">
					立即领取
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			
		},
		data() {
			return {}
		},
		methods: {
			confirm() {
				this.$emit('confirm', {});
			}
		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss" scoped>
	.context {
		height: 120%;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.3);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;
		top: -150rpx;
	}
	
	.context .wrapper .wrapper-content {
		width: 100%;
		height: 100%;
		position: relative;
		top: -90rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.context .wrapper .wrapper-content image {
		width: 530rpx;
		height: 782rpx;
		position: relative;
	}
	
	.context .wrapper .wrapper-content .confirm {
		position: absolute;
		bottom: 48rpx;
		width: 350rpx;
		height: 90rpx;
		opacity: 1;
		background: linear-gradient(180deg, #FFDDAD 0%, #FBA951 100%);
		box-sizing: border-box;
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #C61800;
		font-weight: 500;
		font-size: 38rpx;
	}
	
</style>