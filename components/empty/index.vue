<template>
	<view class="context" v-if="data.length == 0">
		<image v-if="type == '1'" class="icon1" src="/static/images/empty1.png"/>
		<image v-else-if="type == '2'" class="icon2" src="/static/images/empty2.png"/>
		<view class="desc">{{ desc }}</view>
	</view>
</template>

<script>
	export default {
		props: {
			type: {
				type: String,
				default: '1'
			},
			desc: {
				type: String,
				default: "无内容"
			},
			data: {
				type: Array,
				default: []
			}
		},
		data() {
			return {}
		},
		methods: {

		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss" scoped>
	.context {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 120rpx;
	}
	
	.icon1 {
		width: 442rpx;
		height: 274rpx;
	}
	
	.icon2 {
		width: 312rpx;
		height: 354rpx;
	}
	
	.desc {
		margin-top: 40rpx;
		color: $uni-text-color-placeholder;
		font-size: $uni-font-size-base;
	}
</style>