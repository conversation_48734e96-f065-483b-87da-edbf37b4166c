import { login, getUserInfo } from '@/api/index';

// 定义时间格式化函数，明确输入输出类型
export function formatTime(date : string, sym1 : string = '/', sym2 : string = ':') : string {
	const d = new Date(date);
	const y = d.getFullYear();
	const M = String(d.getMonth() + 1).padStart(2, '0');
	const D = String(d.getDate()).padStart(2, '0');
	const h = String(d.getHours()).padStart(2, '0');
	const m = String(d.getMinutes()).padStart(2, '0');
	const s = String(d.getSeconds()).padStart(2, '0');
	return `${y}${sym1}${M}${sym1}${D} ${h}${sym2}${m}${sym2}${s}`;
}

// 转到登录页面函数，明确参数类型
export async function toLogin(account : string, type: string, url : string) {
	let res = await login({
		account: account,
		type: type
	});
	if (res.code == 200) {
		uni.setStorageSync('auth_token', res.token);
		uni.setStorageSync('login_time', new Date());
		if (url) {
			uni.reLaunch({
				url: url,
			})
		}
	} else {
		if(type == '1') { // 微信授权
			let sweixin = null;
			let shares = null;
			plus.share.getServices(function (res) {
				shares = res;
				for (let i = 0; i < res.length; i++) {
					if ("weixin" == res[i].id) {
						sweixin = res[i];
						sweixin.launchMiniProgram({
							id: 'gh_ec2dc110c7cf',
							path: 'pages/auth/index?type=2',
							type: 0 // 0 正式版 2 体验版
						},
							(res2 : any) => {
								uni.setStorageSync('auth_token', res2);
								uni.setStorageSync('login_time', new Date());
								if (url) {
									uni.reLaunch({
										url: url,
									})
								}
							},
							(err2 : any) => {
								console.log(err2)
							});
					}
				}
			}, function (error) {
				console.log("获取分享服务列表失败：" + JSON.stringify(error));
			});
		}
	}
}

// 获取用户信息数据
export async function getUserData() {
	let res = await getUserInfo({});
	if (res.code !== 200) {
		uni.clearStorageSync();
		uni.navigateTo({
			url: '../login/index?url=../home/<USER>',
		})
	} else {
		uni.setStorageSync('sys_user', res.data.user);
		uni.setStorageSync('version', res.data.version);
		uni.setStorageSync('version_expire_time', res.data.versionExpireTime);
		uni.setStorageSync('version_functions', res.data.versionFunctions);
	}
}

// 检查令牌是否过期
export function isTokenExpired() : boolean {
	const curTime = new Date();
	const loginTimeStr = uni.getStorageSync('login_time');
	if (!loginTimeStr) {
		return true;
	}
	const loginTime = new Date(loginTimeStr);
	const ONE_WEEK = 7 * 24 * 60 * 60 * 1000;
	return curTime.getTime() - loginTime.getTime() > ONE_WEEK;
}

// 检查令牌状态
export function tokenStatus() : boolean {
	const authToken = uni.getStorageSync('auth_token');
	let tokenStatus = false;
	if (authToken) {
		tokenStatus = true;
		const isExpired = isTokenExpired();
		if (isExpired) {
			tokenStatus = false;
			uni.clearStorageSync();
		}
	}
	return tokenStatus;
}

// 广告状态检查
export function adStatus() : boolean {
	const versionFunctions = uni.getStorageSync('version_functions') || [];
	const versionExpireTimeStr = uni.getStorageSync('version_expire_time') || '';
	const currentDate = new Date();
	const versionExpireDate = new Date(versionExpireTimeStr);
	if (versionFunctions.indexOf("no_ad") >= 0 && (currentDate.getTime() <= versionExpireDate.getTime())) {
		return false;
	} else {
		return true;
	}
}

// 文本复制功能，根据实现需求添加类型
export function copyText(e : any) : void {
	// 实现文本复制逻辑
}