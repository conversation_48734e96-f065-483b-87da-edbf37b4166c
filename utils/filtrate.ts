export const sliceStr = function (str : string, len : number) {
	len = len || 8;
	if (str != null) {
		if (str.length > len) {
			return str.substring(0, len) + "...";
		} else {
			return str;
		}
	}
	return '';
}

export const highLight = function (source : string, target : string, step : number) {
	let targetLength = target.length;
	let targetData : string[] = [];
	let filterResult : string[] = [];
	// 对目标文字内容进行分段处理
	for (let i = 0; i <= targetLength - step; i++) {
		for (let j = i + step; j <= targetLength; j++) {
			let targetSplit : string = target.substring(i, j);
			targetData.push(targetSplit);
		}
	}
	// 在源文字内容查找目标文字分段
	for (let i in targetData) {
		let target = targetData[i];
		if (source.indexOf(target) >= 0) {
			filterResult.push(target);
		}
	}
	filterResult.sort((a, b) => b.length - a.length)
	return filterResult;
}

export const myReplace = function (content : string) {
	content = content.replace(" ", "&nbsp;");
	if (content.indexOf(" ") != -1) {
		return myReplace(content);
	}
	return content;
}