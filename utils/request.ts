let tokenKey = "auth_token";
let serverUrl = "https://srnz.net:8701";
var aesUrl = '/common/aes';
let exceptionAddrArr = ['/updateScanCodeStatus', '/login','/sendEmailCode',aesUrl];
import {
    AESDecrypt
} from '@/utils/aes/encryption-utils.js';

//请求头处理函数
function createHeader(url : string, type : string)  {
	let header = {
		contentType: 'application/json'
	};
	if (type == 'form') {
		header.contentType = 'application/x-www-form-urlencoded';
	}
	if (exceptionAddrArr.indexOf(url) == -1) {
		let token = uni.getStorageSync(tokenKey);
		header['Authorization'] = token;
	}
	return  header;
}

// 响应拦截器处理函数
function responseInterceptor(res) {
    if(typeof res.data === "string") {
        const aesFlag = uni.getStorageSync('aesFlag');
        uni.setStorageSync('aesFlag',true);
        res.data = AESDecrypt(res.data);
        if(!aesFlag) {
            request('GET',aesUrl,{},'');
        }
    } 
    return res;
}

function request(method: any, url: string, data: any, type: string): Promise<any> {
    const fullUrl = serverUrl + url;
    const header = createHeader(url, type);
	data.dataType = uni.getStorageSync("data_type") ? uni.getStorageSync("data_type") : 0;
    return new Promise((resolve, reject) => {
        uni.request({
            url: fullUrl,
            method,
            data,
            header: header,
            success: (res : any) => {
                if (res.statusCode == 200) {
					if(url === aesUrl) {
						if(res.data.code === 200) {
							uni.setStorageSync('aesFlag',res.data.data);
						}
						resolve(res.data);
					} else {
						const processedRes = responseInterceptor(res);
						resolve(processedRes.data);
					}
                } else {
                    reject(res);
                }
            },
            fail: (err: any) => {
                reject(err);
            }
        });
    });
}

export const get  = (url : string, data : any) : Promise<any> => request('GET', url, data, '');
export const post  = (url : string, data : any) : Promise<any> => request('POST', url, data,'');
export const postParams  = (url : string, data : any) : Promise<any> => request('POST', url, data, 'form');
export const put = (url : string, data : any,) : Promise<any> => request('PUT', url, data,'');
export const del   = (url : string) : Promise<any> => request('DELETE', url, {},'');