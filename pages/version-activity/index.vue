<template>
	<view class="page-body">
		<view class="title">用邀请码得会员</view>
		<view class="code-info">
			<view class="my-code">我的邀请码</view>
			<view class="my-code-info">
				<view class="code">{{myCode}}</view>
				<image class="copy" src="@/static/images/copy.png"></image>
				<view class="desc">剩余可兑换次数 {{exchangeCount}}次</view>
			</view>
			<view class="exchange-code">兑换邀请码</view>
			<view class="exchange-code-info">
				<input type="text" class="input">
				<view class="submit">确认兑换</view>
			</view>
		</view>
		<view class="exchange-info">
			<view class="desc">
				<strong>分享邀请码：</strong>当新用户兑换了你的邀请码后，对方将获得5天尊享版会员；你也将得到3天尊享版会员。通过邀请，最多可累计获得21天尊享版会员。
			</view>
			<view class="desc">
				<strong>
					兑换邀请码：
				</strong>
				注册后24小时内输入他人的邀请码，兑换成功后，你得5天尊享版会员，对方得3天尊享版会员。每人仅能兑换一次邀请码。
			</view>
		</view>
		<view class="task-info">
			<view class="info-1">
				<view class="title">
					做一次性任务得会员 - 最多可领取13天尊享版会员
				</view>
			</view>
			<view class="info-2">
				<view class="task-title">
					反复做任务得会员 - 最多可领取13天尊享版会员
				</view>
				<view>
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				myCode: 'MXHE35hgJD',
				exchangeCount: 5
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
		background-color: #F7FAF9;
	}

	.title {
		font-size: 28rpx;
		font-weight: 600;
		display: flex;
		justify-content: flex-start;
		width: 100%;
		height: 56rpx;
		line-height: 56rpx;
		margin: 16rpx 16rpx 16rpx 56rpx;
	}

	.code-info, .task-info {
		background-color: #fff;
		width: calc(100% - 64rpx);
		display: flex;
		flex-direction: column;
		padding: 16rpx 32rpx 32rpx 32rpx;
		border-radius: 20rpx;
	}

	.code-info .my-code-info {
		display: flex;
		height: 56rpx;
		align-items: center;
		justify-content: flex-start;
	}

	.code-info .my-code-info .code {
		margin: 0 32rpx;
		font-size: $uni-font-size-lm;
	}

	.code-info .my-code-info .copy {
		width: 40rpx;
		height: 40rpx;
		margin-right: 32rpx;
	}

	.code-info .my-code-info .desc {
		color: $uni-color-primary;
		font-size: $uni-font-size-sm;
	}

	.code-info .exchange-code-info {
		display: flex;
	}

	.code-info .exchange-code-info .submit {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx;
		height: 60rpx;
		border-radius: 60rpx;
		background: #46B4B1;
		color: #FFF;
		text-align: center;
		padding: 0 20rpx;
		font-size: 28rpx;
	}

	.code-info .exchange-code-info .input {
		background-color: #F7FAF9;
		width: 268rpx;
		border-bottom: 1px solid $uni-color-primary;
		margin: 0 32rpx;
		height: 56rpx;
	}

	.code-info .my-code,
	.code-info .exchange-code {
		color: $uni-color-primary;
		font-size: $uni-font-size-sm;
		height: 56rpx;
		display: flex;
		align-items: center;
	}
	
	.exchange-info {
		margin-top: 16rpx;
	}
	
	.exchange-info .desc {
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}
	
	.task-info {
		margin-top: 16rpx;
	}
</style>