<template>
	<view class="page-body">
		<image class="logo" src="@/static/images/logo.png" />
		<image class="start-title" src="@/static/images/start-title.png" />
	</view>
</template>

<script>
	import {
		tokenStatus,
	} from '@/utils/util';
	export default {
		data() {
			return {
			}
		},
		methods: {
			
			handleAgreementDetail(type) {
				if (type == 1) {
					uni.navigateTo({
						url: "/pages/service/agreement"
					})
				}
				if (type == 2) {
					uni.navigateTo({
						url: "/pages/service/privicy"
					})
				}
			},
			
			init() {
				let timer = setTimeout(()=>{
					const tokenExist = tokenStatus();
					if(tokenExist) {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					} else {
						uni.reLaunch({
							url: "/pages/login/index"
						})
					}
					clearInterval(timer);
				},2000);
			}
		},
		onLoad(options) {
			this.init();
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
		justify-content: center;
	}

	.logo {
		width: 160rpx;
		height: 186rpx;
	}

	.start-title {
		margin-top: 20rpx;
		width: 450rpx;
		height: 300rpx;
		margin-bottom: 400rpx;
	}
	
</style>