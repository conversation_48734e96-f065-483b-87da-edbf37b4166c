<template>
	<view class="pageView"> 
		<view class="scrollview">
			<scroll-view scroll-y scroll-x show-scrollbar scroll-with-animation :scroll-top="scrollTop" class="scroll-content">
				<view class="chat-content">
					<block v-if="messageList.length">
						<block v-for="(item, index) in messageList" :key="index">
							<view :class="item.role">
								<view class="chatbox" :class="item.role == 'user' ? ' chatbox-right' : 'chatbox-left'">
									<block v-if="item.content">
										<view class="chat_content">
											<block v-if="item.role == 'user'">
												<text space="nbsp" selectable user-select>{{ item.content }}</text>
											</block>
											<block v-else>
												<ul-markdown :source="item.content" />
											</block>
											<block v-if="item.role == 'assistant'">
												<view class="chat_btn" v-if="retryState && index == messageList.length - 1">
													<view class="icon" @click="bindretry">
														<uv-icon name="reload" bold color="#8c8c8c"></uv-icon>
													</view>
												</view>
											</block>
										</view>
									</block>
									<block v-else>
										<view class="loading"><uv-loading-icon mode="circle" size="36rpx"></uv-loading-icon></view>
									</block>
								</view>
							</view>
						</block>
					</block>
					<block v-else>
						<view class="none">{{ text }}</view>
					</block>
				</view>
			</scroll-view>
		</view>
		<!-- 底部输入框 -->
		<view class="bottom">
			<view class="bottom_border" :style="{ marginBottom: `${keyboardHeight}px` }">
				<view class="bottom_textarea">
					<textarea
						cursor-spacing="70"
						@keyboardheightchange="keyboardheightchange"
						auto-height
						v-model="inputText"
						:adjust-position="false"
						maxlength="-1"
						placeholder="询问任何问题"></textarea>
				</view>
				<view class="bottom_button">
					<view class="Upload">  
						<button disabled class="uv-reset-button upload_btn">
							<uv-icon bold name="plus" color="#8c8c8c"></uv-icon>
						</button>
						<button :disabled="!messageList.length" class="uv-reset-button upload_btn" @click="bindtrash">
							<uv-icon bold name="trash" color="#8c8c8c"></uv-icon>
						</button>
					</view>
					<view class="sending">
						<button
							@click="bindSendMessage"
							:disabled="!inputText"
							v-if="!stopState"
							class="uv-reset-button"
							:class="{ 'btn-disabled': inputText }">
							<uv-icon name="arrow-upward" bold :color="inputText ? '#fff' : '#8c8c8c'"></uv-icon>
						</button>
						<button @click="bindSendStop" v-if="stopState" class="uv-reset-button btn-disabled">
							<uv-icon name="pause" bold color="#fff"></uv-icon>
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { fetchDeepSeekStream } from '@/utils/deepseek.js';
	export default {
		data() {
			return {
				scrollTop: 0,
				messageList: [],
				stopStream: null,
				inputText: '',
				keyboardHeight: 0,
				inputMessage: '',
				stopState: false,
				retryState: false,
				text: '有什么可以帮忙的?'
			}
		},
		methods: {
			// 发送消息
			async bindSendMessage () {
				if (!this.inputText.trim()) return;
				// 记录用户消息
				this.messageList.push({
					role: 'user',
					content: this.inputText
				});
				this.inputText = '';
				// 添加占位 AI 消息
				this.messageList.push({
					role: 'assistant',
					content: ''
				});
				this.$nextTick(() => {
					this.scrollIntoView();
				});
				// 获取 AI 消息索引
				const lastIndex = this.messageList.length - 1;
				this.stopState = true;
				this.retryState = false;
				this.fetchDeepSeek(lastIndex);
			},
			
			bindtrash() {
				uni.showModal({
					title: '提示',
					content: '确定要清空所有聊天记录吗？',
					success: (res) => {
						if (res.confirm) {
							this.messageList = [];
							uni.clearStorageSync();
						}
					}
				});
			},
			
			scrollIntoView() {
				this.$nextTick(() => {
					uni.createSelectorQuery()
						.select(`.chat-content`)
						.boundingClientRect((res) => {
							if (res) {
								this.scrollTop = res.height; // 让最新输入的消息位于视图顶部
							}
						})
						.exec();
				});
			},
			
			fetchDeepSeek(lastIndex){
				// 发送请求
				// const chatHistory = [{ role: 'assistant', content: '你是一个 AI 助手,帮用户修改文案' }, ...messageList.value];
				this.stopStream = fetchDeepSeekStream(
					this.messageList,
					(progressText) => {
						this.messageList[lastIndex].content = progressText;
					}, // 逐字显示
					() => {
						console.log('加载完成');
						this.stopState = false;
						uni.setStorageSync('messageList', this.messageList);
					},
					(errCode) => {
						console.error('请求失败:', errCode);
						if (errCode.errMsg === 'request:fail timeout') {
							this.messageList[lastIndex].content = '网络连接失败,请求超时';
							this.retryState = true;
							this.stopState = false;
						}
					},
					() => {
						console.log('请求已手动中断');
					}
				);
			},
			
			bindretry() {
				if (messageList.value.length === 0) return; // 确保有数据可以重试
				const lastIndex = messageList.value.length - 1; // 获取删除前的索引
				retryState.value = false;
				messageList.value[lastIndex].content = '';
				stopState.value = true;
				fetchDeepSeek(lastIndex); // 重新请求
			},
			
			bindSendStop() {
				this.stopStream(); // 终止请求
				stopState.value = false;
				retryState.value = true;
			},
			
			keyboardheightchange(e) {
				keyboardHeight.value = e.detail.height;
			}
		}
	}
</script>

<style lang="scss">
	.none {
		font-weight: 500;
		font-size: 40rpx;
		color: #333333;
		position: absolute;
		top: 50%;
		transform: translate(-50%, -50%);
		left: 50%;
	}
	.pageView {
		display: flex;
		flex-direction: column;
		height: calc(100vh - 108rpx); /* 让整个页面占满屏幕 */
	}
	
	.scrollview {
		flex: 1; /* 让 scroll-view 占据剩余空间 */
		overflow: hidden;
		box-sizing: border-box;
	}
	
	.scroll-content {
		height: 100%;
		box-sizing: border-box;
		// padding: 0 30rpx;
	}
	
	.chat-message {
		background: #fff;
		padding: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
	}
	
	.bottom {
		display: flex;
		align-items: center;
		background: #fff;
		padding: 20rpx;
		padding-top: 4rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
	}
	.bottom_border {
		width: 100%;
		border: 1rpx solid #f5f5f5;
		border-radius: 40rpx;
		padding: 20rpx 24rpx;
		box-shadow: 0rpx 2rpx 4rpx 0rpx #ccc;
		transition: 0.2s;
		.bottom_textarea {
			textarea {
				width: 100%;
				font-weight: 500;
				font-size: 32rpx;
				color: #222222;
				max-height: 400rpx;
			}
		}
		.bottom_button {
			height: 80rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 10rpx;
			.Upload {
				display: flex;
				align-items: center;
				button {
					width: 65rpx;
					height: 65rpx;
					box-sizing: border-box;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 20rpx;
				}
			}
			.sending {
				button {
					width: 65rpx;
					height: 65rpx;
					box-sizing: border-box;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
			.btn-disabled {
				background: #0d0d0d;
				cursor: not-allowed;
			}
		}
	}
	
	.chat-content {
		// width: 100%;
		margin: 0 30rpx;
	}
	
	.chatbox {
		border-radius: 10rpx;
		position: relative;
		font-size: 32rpx;
		color: #333;
		word-break: break-all;
		word-wrap: break-word;
		.chat_content {
			font-size: 28rpx;
			width: fit-content;
			.chat_btn {
				display: flex;
				align-items: center;
				margin-top: 6rpx;
				// justify-content: flex-end;
				.icon {
					margin-right: 10rpx;
				}
			}
		}
	}
	
	.chatbox-left {
		background: #fff;
		display: inline-block;
		justify-content: flex-start;
		width: 100%;
	}
	
	.chatbox-right {
		background: #f5f5f5;
		max-width: 70%;
		padding: 20rpx 22rpx;
	}
	
	.user {
		display: flex;
		align-items: flex-start;
		padding: 20rpx 0rpx;
		justify-content: flex-end;
	}
	.assistant {
		display: flex;
		align-items: flex-start;
		padding: 20rpx 0rpx;
	}
	.loading {
		display: flex;
	}
</style>