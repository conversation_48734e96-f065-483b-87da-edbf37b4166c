<template>
	<load v-if="loadingFlag"></load>
	<view class="page-body">
		<view class="pic">
			<view class="pic-wrapper">
				<image :src="imgUrl" />
			</view>
		</view>
		<view class="title">
			<view class="icon"></view>
			<view class="text-wrapper">
				<view class="text">内容识别结果</view>
			</view>
		</view>
		<view class="scan">
			<textarea ref="inputRef" maxlength="-1" @input="handleTextInput" placeholder="识别内容将展示在此处" :auto-height="true"></textarea>
		</view>
		<view class="form_btn_wrap">
			<view class="form-btn" @click="confirmAnswer">
				<icon class="icon" type="success_no_circle" size="16" color="#fff">
				</icon>
				确认内容
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getOcr
	} from '@/api/index';
	export default {
		data() {
			return {
				imgUrl: '',
				paperId: '',
				questionIndex: 1,
				from: '',
				textValue: '',
				loadingFlag: true,
			}
		},
		methods: {
			handleTextInput(e) {
				this.textValue = e.detail.value;
			},

			handleOcrScan() {
				let filter = {
					imgUrl: this.imgUrl
				}
				getOcr(filter).then(res => {
					let result = '';
					for (let key in res.data.items) {
						let item = res.data.items[key];
						result = result.concat(item.text);
					}
					this.$refs.inputRef.value = result;
					this.textValue = result;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},
			confirmAnswer() {
				let data = {
					scanContent: this.textValue.toString(),
					paperId: this.paperId,
					paperTitle: this.paperTitle,
					questionIndex: this.questionIndex,
					source: this.source
				};
				if (this.from == "detail") {
					uni.navigateTo({
						url: '/pages/detail/index?answerTab=user&ocrParam=' + encodeURIComponent(JSON.stringify(data))
					})
				} else {
					uni.navigateTo({
						url: '/pages/answer-add/index?ocrParam=' + encodeURIComponent(JSON.stringify(data))
					})
				}

			},
		},
		onLoad(options) {
			this.imgUrl = options.imgUrl;
			this.paperId = options.paperId;
			this.paperTitle = options.paperTitle;
			this.questionIndex = options.questionIndex;
			this.from = options.from;
			this.source = options.source;
			this.handleOcrScan();
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.pic {
		width: 100%;
	}

	.pic .pic-wrapper {
		margin-top: 20rpx;
	}

	.pic .pic-wrapper image {
		width: 100%;
		height: 300rpx;
		border-radius: 20rpx;
	}

	.title {
		width: 100%;
		margin-top: 20rpx;
		position: relative;
		display: flex;
	}

	.title .icon {
		display: inline-block;
		position: relative;
		width: 16rpx;
		height: 56rpx;
		border-radius: 20rpx;
		background: #46B4B1;
	}

	.title .text {
		display: flex;
		position: relative;
		left: 20rpx;
		align-content: center;
		justify-content: center;
		width: 200rpx;
		height: 56rpx;
		color: var(--unnamed, #333);
		text-align: center;
	}

	.scan {
		width: calc(100% - 56rpx);
		position: relative;
		word-wrap: break-word;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
		margin-top: 20rpx;
		padding: 20rpx;
	}

	.scan textarea {
		width: 100%;
		color: #000;
		font-size: 28rpx;
		letter-spacing: 1rpx;
		height: auto;
		overflow: hidden;
		resize: none;
	}

	.form_btn_wrap {
		display: flex;
		justify-content: center;
		margin-top: 56rpx;
		margin-bottom: 56rpx;
	}

	.form-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 320rpx;
		height: 88rpx;
		border-radius: 128rpx;
		background: #46B4B1;
		color: #FFF;
		text-align: center;
		font-size: 20px;
	}

	.form-btn .icon {
		margin-right: 20rpx;
	}
</style>