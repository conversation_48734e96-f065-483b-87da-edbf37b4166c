<template>
	<load v-if="loadingFlag"></load>
	<view class="page-body">
		<view class="search">
			<image class="search-icon" src="@/static/images/search.png" />
			<input ref="inputRef" class="search-content" type='text' :placeholder='placeholder' @confirm="handleSearch" />
		</view>
		<view class="type-list">
			<view @click="selectType(index)" class="type-section" v-for="(item, index) in typeList" :key="index">
				<text class="title">{{item["title"]}}</text>
				<view class="show_bar" v-if="typeIndex == index"></view>
			</view>
		</view>
		<view v-if="typeIndex == 0" class="select-area">
			<text class="select" @click="pickerShow = true;">{{areaIndex >= 0 ? areaStrList[areaIndex] : '请选择'}}</text>
			<image class="down" src="@/static/images/down.png" />
		</view>
		<scroll-view class="list" @scrolltolower="scrolltolower" lower-threshold="200" :scroll-y="true">
			<empty v-if="typeIndex == 0 && showEmpty" type="2" desc = '无试卷数据,请选择你所在的省份吧!' :data = 'paperList'></empty>
			<view v-if="typeIndex == 0">
				<view class="list-item" @click="handleClickItem(item.paperId)"
					v-for="(item, key) in paperList" :key="key">
				<text class="item-left">
					{{item.title}}
				</text>
				</view>
			</view>
			<empty v-if="typeIndex == 1 && showEmpty" type="2" desc = '无区域数据,请输入正确的省份名称!' :data = 'areaList'></empty>
			<view v-if="typeIndex == 1">
				<view class="list-item" @click="handleClickItem(item.areaId)"
					v-for="(item, key) in areaList" :key="key">
				<text class="item-left">
					{{item.content}}
				</text>
				<text class="item-middle">共{{item.paperCount}}套</text>
				<image class="item-right" src="@/static/images/enter2.png" />
				</view>
			</view>
			<empty v-if="typeIndex == 2 && showEmpty" type="2" desc = '未搜索到题目~请尽量避免标点,减少搜索字数噢!' :data = 'questionList'></empty>
			<view v-if="typeIndex == 2">
				<view class="list-item"
					@click="handleClickItem(item.paperId + ',' + item.questionIndex)" v-for="(item, key) in questionList" :key="key">
				<view class="question-item">
					<rich-text :nodes="item.content"></rich-text>
				</view>
				</view>
			</view>
		</scroll-view>
		<view v-if="pickerShow" class="bigMorePicker" @click="closePicker">
			<view class="morePickerCent">
				<picker-view :indicator-style="indicatorStyle" :value="selectedArea" @change="bindPickerChange"
					class="picker-view">
					<picker-view-column>
						<text class="item" v-for="(item,index) in areaStrList" :key="index">{{item}}</text>
					</picker-view-column>
				</picker-view>
				<view class="morePickerTitle">
					<button size="default" type="default" class="cancel" @click="closePicker">取消</button>
					<button size="default" type="default" class="confirm" @click="confirmPicker">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		highLight
	} from '@/utils/filtrate';
	import {
		getAreaList,
		getPaperList,
		getQuestionList
	} from '@/api/index';
	export default {
		data() {
			return {
				loadingFlag: true,
				showEmpty: false,
				areaList: [],
				paperList: [],
				questionList: [],
				areaStrList: [],
				queryContent: '',
				placeholder: "搜索年份、试卷类型",
				hasMore: false,
				pageNum: 1,
				pageSize: 20,
				typeList: [{
						title: "我的省份",
						index: 0
					},
					{
						title: "所有省份",
						index: 1
					},
					{
						title: "直搜题目",
						index: 2
					}
				],
				typeIndex: 0,
				indicatorStyle: "border-top:1rpx solid #999; border-bottom:1rpx solid #999; height: 100rpx;",
				selectedArea: [
					[0]
				],
				areaIndex: -1,
				areaOffset: uni.getStorageSync("data_type") ? uni.getStorageSync("data_type") + 1 : 0,
				pickerShow: false,
			}
		},
		methods: {

			handleSearch(e) {
				this.queryContent = e.detail.value;
				this.pageNum = 1;
				this.loadingFlag = true;
				let filter = {
					content: this.queryContent
				};
				if (this.typeIndex == 0) {
					this.paperList = [];
					filter.areaId = this.areaIndex + 1 + this.areaOffset;
					filter.title = filter.content;
					this.selectPaperList(filter);
				} else if (this.typeIndex == 1) {
					this.areaList = [];
					this.selectAreaList(filter);
				} else if (this.typeIndex == 2) {
					this.questionList = [];
					this.selectQuestionList(filter);
				}
			},

			handleClickItem(e) {
				if (this.typeIndex == 0) {
					uni.navigateTo({
						url: '/pages/detail/index?paperId=' + e
					})
				} else if (this.typeIndex == 1) {
					uni.navigateTo({
						url: '/pages/paper/index?areaId=' + e,
					})
				} else if (this.typeIndex == 2) {
					let splits = e.split(",");
					uni.navigateTo({
						url: '/pages/detail/index?paperId=' + splits[0] + '&questionIndex=' + splits[1],
					})
				}
			},

			selectType(index) {
				this.typeIndex = index;
				this.$nextTick(() => {
					this.$refs.inputRef.value = '';
				});
				this.pageNum = 1;
				let filter = {
					content: this.queryContent
				};
				if (index == 0) {
					this.placeholder = "搜索年份、试卷类型";
					this.selectAreaList({});
					let areaIndex = uni.getStorageSync('area_index');
					if (areaIndex || areaIndex === 0) {
						this.areaIndex = areaIndex;
						this.selectedArea = [
							[areaIndex]
						];
						this.selectPaperList({
							areaId: Number(areaIndex) + 1 + this.areaOffset
						});
					} else {
						this.loadingFlag = false;
					}
				} else if (index == 1) {
					this.placeholder = "搜索区域";
					this.areaList = [];
					this.selectAreaList(filter);
				} else if (index == 2) {
					this.placeholder = "搜索题目";
					this.questionList = [];
				}
			},

			bindPickerChange(e) {
				this.selectedArea[0] = e.detail.value;
			},

			selectAreaList(filter) {
				this.showEmpty = false;
				filter.pageSize = 100;
				filter.pageNum = 1;
				getAreaList(filter).then(res => {
					this.areaList = res.rows;
					this.areaStrList = this.areaList.map(item => item.content);
					this.loadingFlag = false;
					// 区域列表时才展示空组件
					if(this.typeIndex == 1) {
						this.showEmpty = true;
					}
				}).catch(err => {
					console.log(err)
				});
			},

			scrolltolower() {
				if (this.hasMore) {
					let filter = {
						content: this.queryContent
					};
					if (this.typeIndex == 0) {
						filter.areaId = this.areaIndex + 1 + this.areaOffset;
						filter.title = filter.content;
						this.selectPaperList(filter);
					} else if (this.typeIndex == 1) {
						// 区域一次性全加载
					} else if (this.typeIndex == 2) {
						this.selectQuestionList(filter);
					}
				}
			},

			selectPaperList(filter) {
				this.showEmpty = false;
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getPaperList(filter).then(res => {
					let list = this.paperList.concat(res.rows);
					let nextPage = ++this.pageNum;
					if (list.length < res.total) {
						this.hasMore = true;
					} else {
						this.hasMore = false;
					}
					this.paperList = list;
					this.pageNum = nextPage;
					this.loadingFlag = false;
					this.showEmpty = true;
				}).catch(err => {
					console.log(err)
				});
			},

			selectQuestionList(filter) {
				this.showEmpty = false;
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getQuestionList(filter).then(res => {
					let temp = [];
					for (let key in res.rows) {
						let target = {};
						let item = res.rows[key].content;
						let split = item.split("<p>要求");
						if (split.length >= 2) {
							item = split[split.length - 2];
						}
						if (this.queryContent.length > 0) {
							let queryContentArr = this.queryContent.split(" ");
							for (let key1 in queryContentArr) {
								let queryContent = queryContentArr[key1];
								let result = highLight(item, queryContent, queryContent.length);
								for (let key2 in result) {
									item = item.replace(new RegExp(result[key2], "gm"),
										`<span style="color:#FF9C07;">${result[key2]}</span>`)
								}
							}
						}
						target.content = item;
						target.paperId = res.rows[key].paperId;
						target.questionIndex = res.rows[key].questionIndex;
						temp.push(target);
					}
					let list = this.questionList.concat(temp);
					let nextPage = ++this.pageNum;
					if (list.length < res.total) {
						this.hasMore = true;
					} else {
						this.hasMore = false;
					}
					this.questionList = list;
					this.pageNum = nextPage;
					this.loadingFlag = false;
					this.showEmpty = true;
				}).catch(err => {
					console.log(err)
				});
			},

			closePicker() {
				this.pickerShow = false;
			},

			confirmPicker() {
				this.areaIndex = this.selectedArea[0][0];
				uni.setStorageSync('area_index', this.areaIndex);
				this.pageNum = 1;
				this.paperList = [];
				this.selectPaperList({
					areaId: this.areaIndex + 1 + this.areaOffset
				});
			},
		},
		onLoad() {
			this.selectType(0);
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.search {
		width: 100%;
		height: 70rpx;
		display: flex;
		flex-direction: row;
		border-radius: 32rpx;
		background: rgba(70, 180, 177, 0.1);
		align-items: center;
	}

	.search .search-icon {
		margin-left: 20rpx;
		width: 32rpx;
		height: 32rpx;
	}

	.search .search-content {
		margin-left: 20rpx;
		width: 85%;
		color: $uni-text-color-grey;
		font-size: 28rpx;
	}

	.type-list {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		width: 100%;
		margin-top: 20rpx;
	}

	.type-list .type-section {
		display: flex;
		flex-direction: column;
		margin-right: 30rpx;
	}

	.type-list .type-section .title {
		font-weight: bold;
		font-size: 28rpx;
		color: $uni-text-color;
	}

	.type-list .type-section .show_bar {
		height: 12rpx;
		border-radius: 20rpx;
		background: $uni-color-primary;
	}

	.select-area {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 100%;
	}

	.select-area .select {
		color: $uni-color-primary;
		font-weight: bold;
		font-size: 28rpx;
	}

	.select-area .down {
		width: 24rpx;
		height: 12rpx;
	}

	.list {
		width: 100%;
		margin-top: 20rpx;
		position: relative;
		flex: 1;
		height: 1px;
	}

	.list .list-item {
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		border-radius: 20rpx;
		min-height: 100rpx;
		background: rgba(70, 180, 177, 0.1);
	}

	.list .list-item .item-left {
		padding: 16rpx 32rpx;
		font-size: 28rpx;
	}

	.list .list-item .question-item {
		padding: 16rpx 32rpx;
		color: $uni-text-color;
		line-height: 40rpx;
		font-size: 28rpx;
	}

	.list .list-item .item-middle {
		position: relative;
		color: $uni-text-color-grey;
		font-size: 28rpx;
		margin-left: auto;
		margin-right: 20rpx;
	}

	.list .list-item .item-right {
		width: 40rpx;
		height: 40rpx;
		justify-content: flex-end;
		margin-right: 20rpx;
		font-size: 28rpx;
	}

	.bigMorePicker {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.6);
		z-index: 99;
	}

	.morePickerCent {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		border-radius: 14rpx;
	}

	.picker-view {
		height: 500rpx;
		margin-top: 20rpx;
		padding: 0 56rpx;
	}

	.morePickerTitle {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: center;
		padding: 40rpx 0;
	}

	.morePickerTitle .confirm,
	.cancel {
		margin: 0 28rpx;
		border-radius: 14rpx;
		padding: 0rpx 72rpx;
	}

	.morePickerTitle .cancel::after,
	.confirm::after {
		border: none;
	}

	.morePickerTitle .cancel {
		border: 1rpx solid $uni-text-color-disable;
	}

	.morePickerTitle .confirm {
		background-color: $uni-color-primary;
		color: #fff;
	}


	.picker-view .item {
		line-height: 100rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		color: $uni-text-color;
	}
</style>
