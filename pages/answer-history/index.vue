<template>
	<load v-if="loadingFlag"></load>
	<empty type="1" desc = '无历史答案,快去上传自己的答案吧!' :data = 'userAnswerList'></empty>
	<view class="page-body">
		<scroll-view class="list" :scroll-y="true" lower-threshold="200" @scrolltolower="scrolltolower">
			<view v-for="(item, index) in userAnswerList" :key="index">
				<view class="list-item" @click="handleClickAnswer(item.paperId)">
					<view class="item-left">
						<image src="@/static/images/play-icon.png"></image>
					</view>
					<view class="item-middle">
						<view class="item-title">{{item.title}}</view>
						<view class="item-info">已上传{{item.num}}次</view>
					</view>
					<view class="item-right">
						<image src="@/static/images/enter2.png" />
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		getUserAnswerList
	} from '@/api/index';
	export default {
		data() {
			return {
				loadingFlag: true,
				userAnswerList: [],
				hasMore: false,
				pageNum: 1,
				pageSize: 20
			}
		},
		methods: {
			selectUserAnswers(filter) {
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getUserAnswerList(filter).then(res => {
					let array = [];
					for (let key in res.rows) {
						let paper = res.rows[key].paper;
						let index = array.findIndex(item => {
							return item.paperId === paper.paperId;
						});
						if (index < 0) {
							paper.num = 1;
							array.push(paper);
						} else {
							array[index].num++;
						}
					}
					res.rows = array;
					var list = this.userAnswerList.concat(res.rows);
					var nextPage = ++this.pageNum;
					if (list.length < res.total) {
						this.hasMore = true;
					} else {
						this.hasMore = false;
					}
					this.userAnswerList = list;
					this.pageNum = nextPage;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},

			loadMore() {
				if (this.hasMore) {
					let userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
					if (userId) {
						let filter = {
							userId: userId,
						}
						this.selectUserAnswers(filter);
					}
				}
			},
			handleClickAnswer(paperId) {
				uni.navigateTo({
					url: '/pages/answer/index?paperId=' + paperId,
				})
			},
		},
		onLoad(options) {
			var userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
			if (userId) {
				let filter = {
					userId: userId,
				}
				this.selectUserAnswers(filter);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.list {
		width: 100%;
		margin: 20rpx 0rpx;
		flex: 1;
		height: 1px;
	}

	.list .list-item {
		position: relative;
		display: flex;
		flex-direction: row;
		background: rgba(70, 180, 177, 0.1);
		border-radius: 20rpx;
		margin-bottom: 20rpx;
	}

	.list .list-item .item-left {
		position: relative;
		display: inline-block;
		left: 30rpx;
		top: 30rpx;
		width: 36rpx;
		height: 36rpx;
	}

	.list .list-item .item-left image {
		width: 36rpx;
		height: 36rpx;
		position: relative;
	}

	.list .list-item .item-middle {
		display: inline-block;
		position: relative;
		left: 50rpx;
		width: 480rpx;
	}

	.list .list-item .item-middle .item-title {
		font-size: 28rpx;
		position: relative;
		margin-top: 30rpx;
		line-height: 40rpx;
	}

	.list .list-item .item-middle .item-info {
		position: relative;
		margin-top: 10rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		color: #666;
		font-size: 24rpx;
		line-height: 48rpx;
	}

	.list .list-item .item-right {
		display: inline-block;
		position: relative;
		margin-left: auto;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-content: center;
		right: 30rpx;
	}

	.list .list-item .item-right image {
		width: 40rpx;
		height: 40rpx;
	}
</style>
