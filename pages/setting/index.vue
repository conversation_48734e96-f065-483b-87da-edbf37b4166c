<template>
	<page-meta :page-font-size="currentFontSize+'px'" :root-font-size="currentFontSize+'px'"></page-meta>
	<view class="content">
		<view class="slider-container">
			<view class="title">囊中对比</view>
			<slider
				:min="12"
				:max="20"
				:value="currentFontSize"
				@changing="handleSliderMove"
				@change="handleSliderEnd"
				:step="2"
				activeColor="#46B4B1"
				block-size="20"
			/>
			<view class="size-labels">
				<text 
					v-for="size in fontSizes" 
					:key="size"
					:class="{ 'active': currentFontSize === size }"
					:style="{ fontSize: size + 'px' }"
				>
					{{ sizeMap[size] }}
				</text>
			</view>
		</view>

		<view class="action">
			<button class="confirm-btn" @click="saveSettings">确定</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentFontSize: 14,  // 默认居中
			fontSizes: [12, 14, 16, 18,20],
			sizeMap: {
				12: '小号', 
				14: '默认',
				16: '大号',
				18: '超大',
				20: '巨大'
			}
		};
	},
	mounted() {
		this.loadSettings();
	},
	methods: {
		loadSettings() {
			const savedSize = uni.getStorageSync("fontSize");
			this.currentFontSize = savedSize || 14;
		},
		handleSliderMove(e) {
			// 实时预览时不立即更新状态
		},
		handleSliderEnd(e) {
			const targetValue = e.detail.value;
			// 对齐到最近的刻度值
			this.currentFontSize = this.fontSizes.reduce((prev, curr) => {
				return (Math.abs(curr - targetValue) < Math.abs(prev - targetValue) ? curr : prev);
			});
		},
		saveSettings() {
			uni.setStorageSync("fontSize", this.currentFontSize);
			uni.showToast({
				title: '设置成功',
				icon: 'success',
				duration: 1500
			});
			uni.navigateBack();
		}
	}
};
</script>

<style lang="scss" scoped>
.title {
	font-size: 1rem;
	text-align: center;
	color: #333;
}

.slider-container {
	width: 90%;
	margin: 10px auto;
	padding: 15px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.size-labels {
	display: flex;
	justify-content: space-between;
	margin-top: 15px;
	
	text {
		color: #666;
		transition: color 0.3s;
		
		&.active {
			color: #46B4B1;
			font-weight: 500;
		}
	}
}

.action {
	display: flex;
	justify-content: center;
	margin-top: 30px;
	width: 100%;
}

.confirm-btn {
	width: 140px;
	background: $uni-color-primary;
	color: white;
	border-radius: 25px;
	font-size: 16px;
	
	&:active {
		opacity: 0.8;
	}
}
</style>
