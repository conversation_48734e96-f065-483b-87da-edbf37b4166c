<template>
	<view class="page-body">
		<view class="app_bar">
			个人中心
		</view>
		<image class="background" src="@/static/images/background-color.png" />
		<view class="info">
			<view class="icon">
				<image class="inner-wrapper" v-if="sysUser && sysUser.avatar" :src="sysUser.avatar" />
				<image class="inner-wrapper" v-else src="@/static/images/default-user.png" />
			</view>
			<view class="user-data">
				<view class="nickname" v-if="sysUser">
					{{sysUser.nickName ? sysUser.nickName : '囊友'}}
				</view>
				<view class="nickname" v-else @click="clickLogin">
					点击登录
				</view>
				<view class="user-number" v-if="sysUser">
					囊友号：{{sysUser.userNumber}}
					<view class="copy">
						<image src="@/static/images/copy.png" @click="handleCopyUserNumber" />
					</view>
				</view>
			</view>
			<view class="edit" @click="handleEditInfo">
				编辑资料 >
			</view>
		</view>
		<view class="versionInfo" v-if="versionTag">
			<image class="bgc" src="@/static/images/version-bgc.png" />
			<view :class="versionTag == '已过期' ? 'versionTag versionTagExpire' : 'versionTag'">
				{{versionTag == '已过期' ? (versionTag + expireDays + '天') : versionTag}}
			</view>
			<view class="versionExpireTime" v-if="versionTag !== '已过期'">
				有效期至：{{versionExpireTime}}
			</view>
			<view class="continueVersion" v-if="versionTag == '已过期'" @click="openVersion">
				<text>立即续费</text>
			</view>
		</view>
		<view class="functions">
			<view class="suggest" @click="suggestReport">
				<view class="icon">
					<image src="@/static/images/point.png" />
				</view>
				软件优化建议
				<view class="item-right">
					<image src="@/static/images/enter2.png" />
				</view>
			</view>
			<view class="agreement" @click="agreement">
				<view class="icon">
					<image src="@/static/images/point.png" />
				</view>
				服务协议
				<view class="item-right">
					<image src="@/static/images/enter2.png" />
				</view>
			</view>
			<view class="privicy" @click="privicy">
				<view class="icon">
					<image src="@/static/images/point.png" />
				</view>
				隐私政策
				<view class="item-right">
					<image src="@/static/images/enter2.png" />
				</view>
			</view>
		</view>
		<view class="login-out" type="default" @click="goback">
			退出登录
		</view>
		<view v-if="adStatus" style="width: 100%;">
			<ad unit-id="adunit-2b3889d8c09d8b55"></ad>
		</view>
	</view>
</template>

<script>
	import {
		adStatus,
		tokenStatus,
		getUserData,
	} from '@/utils/util';
	export default {
		data() {
			return {
				showCalendar: true,
				calendarList: [],
				sysUser: null,
				userAnswerList: [],
				hasMore: true,
				adStatus: true,
				versionTag: '',
				expireDays: null,
				authType: null,
				authToken: ''
			}
		},
		methods: {
			goback() {
				// 退出登录  清空本地存储 不能清空所有,只需要清空userInfo和token
				uni.removeStorageSync('auth_token');
				uni.removeStorageSync('login_time');
				uni.removeStorageSync('sys_user');
				uni.removeStorageSync('version');
				uni.removeStorageSync('version_expire_time');
				uni.removeStorageSync('version_functions');
				uni.reLaunch({
					url: '/pages/login/index'
				});
			},

			suggestReport() {
				uni.navigateTo({
					url: '/pages/suggest/index',
				})
			},
			
			agreement() {
				uni.navigateTo({
					url: '/pages/service/agreement',
				})
			},
			
			privicy() {
				uni.navigateTo({
					url: '/pages/service/privicy',
				})
			},
			
			clickLogin() {
				uni.navigateTo({
					url: '/pages/login/index?url=/pages/mime/index',
				})
			},

			handleCopyUserNumber() {
				let userNumber = this.sysUser.userNumber;
				uni.setClipboardData({
					data: userNumber,
					success: function(res) {
						uni.showToast({
							title: '复制成功',
							icon: 'success',
							duration: 1000
						});
					}
				});
			},

			handleEditInfo() {
				const tokenExist = tokenStatus();
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/mime/index',
					})
				} else {
					uni.navigateTo({
						url: '/pages/info/index',
					})
				}
			},

			openVersion() {
				uni.navigateTo({
					url: '/pages/version/index',
				})
			}
		},
		
		onLoad(options) {
			if (options.authType) {
				this.authType = options.authType;
				this.authToken = uni.getStorageSync('auth_token');
			}
		},
		
		async onShow() {
			const tokenExist = tokenStatus();
			if (!tokenExist) {
				uni.redirectTo({
					url: '/pages/login/index?url=/pages/mime/index'
				})
			} else {
				await getUserData();
				let sysUser = uni.getStorageSync('sys_user');
				let version = uni.getStorageSync('version') || '';
				let versionExpireTime = uni.getStorageSync('version_expire_time') || '';
				let versionTag = '';
				if (version == 'clean') {
					versionTag = '纯净版'
				} else if (version == 'vip') {
					versionTag = '会员版';
				} else if (version == 'pro') {
					versionTag = '专业版';
				}
				let currentDate = new Date();
				let versionExpireDate = new Date(versionExpireTime);
				let expireDays = 0;
				if (currentDate > versionExpireDate) {
					versionTag = '已过期'
					let diffInMs = Math.abs(currentDate.getTime() - versionExpireDate.getTime());
					// 转换为天数（向下取整，不考虑时、分、秒）
					expireDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
				}
				if (versionExpireTime) {
					versionExpireTime = versionExpireTime.split("T")[0];
				}
				this.adStatus = adStatus();
				if (sysUser) {
					this.sysUser = sysUser;
					this.versionExpireTime = versionExpireTime;
					this.versionTag = versionTag;
					this.expireDays = expireDays;
				}
			}
		}
	}
</script>


<style lang="scss" scoped>
	.app_bar {
		margin-top: var(--status-bar-height);
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: center;
		height: 88rpx;
		line-height: 88rpx;
	}

	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.background {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: -10;
	}

	.info {
		width: 100%;
		height: 180rpx;
		display: flex;
		align-items: center;
	}

	.info .icon {
		width: 108rpx;
		height: 108rpx;
		stroke-width: 12rpx;
	}

	.info .inner-wrapper {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.info .user-data {
		margin-left: 32rpx;
		display: flex;
		flex-direction: column;
	}

	.info .user-data .nickname {
		flex-shrink: 0;
		font-size: 36rpx;
		font-weight: 600;
	}

	.info .user-data .user-number {
		margin-top: 6rpx;
		flex-shrink: 0;
		font-size: 24rpx;
		font-weight: 400;
	}

	.info .user-data .user-number {
		display: flex;
	}

	.info .user-data .user-number .copy {
		padding: 0 12rpx 12rpx 12rpx;
	}

	.info .user-data .user-number .copy image {
		width: 30rpx;
		height: 30rpx;
	}

	.info .edit {
		font-size: 24rpx;
		font-weight: 400;
		margin-left: auto;
	}

	.versionInfo {
		width: calc(100% - 120rpx);		
		height: 64rpx;
		background: linear-gradient(90deg, #E7D1A6 0%, #CCAA78 100%);
		border-radius: 20px;
		display: flex;
		justify-content: space-between;
		padding: 0 60rpx;
		align-items: center;
		color: #FFF;
		position: relative;
	}

	.versionInfo .bgc {
		position: absolute;
		left: -50rpx;
		top: -50rpx;
		width: 128rpx;
		height: 128rpx;
	}

	.versionInfo .versionTag {
		font-size: 36rpx;
	}

	.versionInfo .versionTagExpire {
		font-size: 28rpx;
	}

	.versionInfo .versionExpireTime {
		font-size: 24rpx;
		position: relative;
	}

	.versionInfo .continueVersion {
		display: flex;
		align-items: center;
	}

	.versionInfo .continueVersion image {
		width: 44rpx;
		height: 72rpx;
		margin-left: 16rpx;
	}

	.functions {
		display: block;
		margin-top: 32rpx;
		height: auto;
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 24rpx 0rpx;
	}

	.functions .answer-history,
	.functions .note,
	.functions .suggest,
	.functions .agreement,
	.functions .privicy,
	.functions .collect,
	.functions .customer {
		display: flex;
		height: 60rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		color: #333;
		font-size: 28rpx;
		padding: 18rpx 42rpx;
	}


	.functions .customer {
		background-color: #fff;
		margin-bottom: 26rpx;
	}

	.functions .customer::after {
		border-bottom: none;
		z-index: -10;
	}

	.functions .icon {
		margin-right: 24rpx;
	}

	.functions .icon image {
		height: 28rpx;
		width: 28rpx;
	}

	.functions .item-right {
		margin-left: auto;
	}

	.functions .item-right image {
		height: 24rpx;
		width: 24rpx;
	}
	
	.login-out {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		margin-top: 40rpx;
		width: 100%;
		height: 90rpx;
		border-radius: 64rpx;
		background-color: $uni-bg-color !important;
		color: $uni-text-color !important;
	}
</style>