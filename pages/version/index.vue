<template>
	<load v-if="loadingFlag"></load>
	<fui-toast ref="toast"></fui-toast>
	<view class="container">
		<view class="page-body">
			<view class="version-info">
				<view class="version-title">
					<view class="version-title-item" :class="versionId == 1 ? 'activity': ''" @click="versionId = 1">
						纯净版
						<view class="line" v-show="versionId == 1"></view>
					</view>
					<!-- <view class="version-title-item" :class="versionId == 2 ? 'activity': ''" @click="versionId = 2">
						尊享版
						<view class="line" v-show="versionId == 2"></view>
					</view>
					<view class="version-activity">
						<image src="@/static/images/version-activity.png"></image>
						<view class="desc" @click="handleVersionActivity">免费领会员</view>
					</view> -->
				</view>
				<view class="version-desc">
					您的小小支持能为小囊带来无限动力~
				</view>
				<view class="price-wrapper">
					<view v-for="(item, index) in versionMealList" v-key="index" @click="handleSelect(index + 1)"
						:class="selected == index + 1 ? 'price-card active' : 'price-card'">
						<view :class="selected == index + 1 ? 'period active' : 'period'">{{item.period}}个月</view>
						<view :class="selected == index + 1 ? 'price active' : 'price'">￥{{item.currentPrice}}</view>
						<view v-if="index + 1 == 1" style="text-decoration: none;"
							:class="selected == index + 1 ? 'desc active' : 'desc'">0.16元/天</view>
						<view v-else :class="selected == index + 1 ? 'desc active' : 'desc'">￥{{item.originalPrice}}
						</view>
					</view>
				</view>
				<image class="about-version" src="@/static/images/about-version.png" mode="aspectFit"/>
				<!-- <view class="version-function-info">
					<view class="header">
						<view>
							会员功能对比
						</view>
						<view>
							普通版
						</view>
						<view>
							纯净版
						</view>
						<view>
							尊享版
						</view>
					</view>
					<view class="function-item" v-for="(item,index) in functionList">
						<view class="desc">{{item.desc}}</view>
						<view class="version-type" v-if="typeof item.versionType0Desc == 'string'">
							{{item.versionType0Desc + item.versionType0Unit}}
						</view>
						<view class="version-type" v-if="typeof item.versionType0Desc == 'boolean'">
							<image v-if="item.versionType0Desc" src="@/static/images/function-true.png"
								class="function-state"></image>
							<image v-else src="@/static/images/function-error.png"  class="function-state"></image>
						</view>
						<view  class="version-type" v-if="typeof item.versionType1Desc == 'string'">
							{{item.versionType1Desc + item.versionType1Unit}}
						</view>
						<view class="version-type" v-if="typeof item.versionType1Desc == 'boolean'">
							<image v-if="item.versionType1Desc" src="@/static/images/function-true.png"
								class="function-state"></image>
							<image v-else src="@/static/images/function-error.png" class="function-state"></image>
						</view>
						<view class="version-type" v-if="typeof item.versionType2Desc == 'string'">
							{{item.versionType2Desc + item.versionType2Unit}}
						</view>
						<view class="version-type" v-if="typeof item.versionType2Desc == 'boolean'">
							<image v-if="item.versionType2Desc" src="@/static/images/function-true.png"
								class="function-state"></image>
							<image v-else src="@/static/images/function-error.png" class="function-state"></image>
						</view>
					</view>
				</view> -->
			</view>
			<view class="oper-area">
				<view class="bottom-line"></view>
				<view class="button-version" @click="openVersion">
					<view>
						￥{{payAmount}}
					</view>
					<view>
						立即开通
					</view>
				</view>
				<!-- <view class="server-desc">订阅即视为同意我们的购买须知和应用政策</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getVersionMealList,
		validateApplePayment,
		insertUserVersion,
		createOrder,
		activePay,
		appPayConfig
	} from '@/api/index';
	import {
		tokenStatus
	} from "@/utils/util"
	export default {
		data() {
			return {
				loadingFlag: true,
				versionId: 1,
				selected: 2,
				pageNum: 1,
				pageSize: 20,
				versionMealList: [],
				payAmount: 0,
				platform: '',
				submitDesc: '长按识别二维码',
				functionList: [{
						desc: '机构答案',
						versionType0Desc: '3',
						versionType0Unit: '家',
						versionType1Desc: '4',
						versionType1Unit: '家',
						versionType2Desc: '4',
						versionType2Unit: '家',
					},
					{
						desc: '网友补充答案',
						versionType0Desc: '1',
						versionType0Unit: '份',
						versionType1Desc: '不限',
						versionType1Unit: '',
						versionType2Desc: '不限',
						versionType2Unit: '',
					},
					{
						desc: '免广告（小程序内）',
						versionType0Desc: false,
						versionType0Unit: '',
						versionType1Desc: true,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '申论规范词',
						versionType0Desc: false,
						versionType0Unit: '',
						versionType1Desc: false,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '题型专项练习',
						versionType0Desc: false,
						versionType0Unit: '',
						versionType1Desc: false,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '精准搜题',
						versionType0Desc: '3',
						versionType0Unit: '次/天',
						versionType1Desc: '3',
						versionType1Unit: '次/天',
						versionType2Desc: '不限',
						versionType2Unit: '',
					},
					{
						desc: '对比机构答案重合度',
						versionType0Desc: true,
						versionType0Unit: '',
						versionType1Desc: true,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '机构答案与原文对比',
						versionType0Desc: false,
						versionType0Unit: '',
						versionType1Desc: false,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '答案导出PDF',
						versionType0Desc: false,
						versionType0Unit: '',
						versionType1Desc: false,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '拍照识别转文字',
						versionType0Desc: '1',
						versionType0Unit: '次/天',
						versionType1Desc: true,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '语音识别转文字',
						versionType0Desc: '1',
						versionType0Unit: '次/天',
						versionType1Desc: true,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
					{
						desc: '答案复制',
						versionType0Desc: false,
						versionType0Unit: '',
						versionType1Desc: false,
						versionType1Unit: '',
						versionType2Desc: true,
						versionType2Unit: '',
					},
				],
				iap: null,
				ids: ['VIP_1_MONTH', 'VIP_3_MONTH', 'VIP_6_MONTH'],
				vipRuleList: [],
				targetVipRule: null,
			}
		},
		methods: {
			handleSelect(index) {
				this.selected = index;
				this.payAmount = this.versionMealList[index - 1].currentPrice;
			},

			getVersionMeals(filter) {
				uni.getSystemInfo().then(res => {
					this.platform = res.osName;
				});
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				filter.versionId = 1;
				getVersionMealList(filter).then(res => {
					this.versionMealList = res.rows;
					this.payAmount = res.rows[this.selected - 1].currentPrice;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},

			handleVersionActivity() {
				uni.navigateTo({
					url: "/pages/version-activity/index"
				})
			},

			// 获取支付通道
			getChannels() {
				let that = this;
				plus.payment.getChannels((channels) => {
					let payType = 'appleiap';
					let channel = channels.find(i => i.id === payType);
					that.iap = channel ? channel : null
					that.requestOrder();
				}, function(e) {
					console.log(e);
				});
			},

			// 获取内购项目列表
			requestOrder() {
				this.vipRuleList = []
				this.iap.requestOrder(
					this.ids,
					res => {
						uni.hideLoading()
						this.vipRuleList = res
					},
					(errormsg) => {
						console.log(JSON.stringify(errormsg))
					}
				)
			},

			requestPayment(orderNo) {
				uni.showLoading({
					title: '支付中请勿关闭...'
				})
				uni.requestPayment({
					provider: 'appleiap',
					orderInfo: {
						productid: this.targetVipRule.productid, // 需要处理
						username: this.userNumber + '-' + orderNo,
						optimize: true // 设置 optimize: true 解决丢单问题  
					},
					success: async (e) => {
						console.log(e, '支付成功');
						uni.hideLoading()
						if (e.errMsg == 'requestPayment:ok') {
							//在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
							let res = await validateApplePayment({
								orderNo: orderNo,
								username: this.userNumber,
								payment: this.payAmount,
								transactionIdentifier: e.transactionIdentifier,
								transactionReceipt: e.transactionReceipt,
								payTime: e.transactionDate
							});
							// 关闭订单
							if (res.code == 200) {
								await this.finishTransaction(e);
								uni.navigateTo({
									url: '/pages/pay-result/index?status=success',
								});
							} else {
								uni.navigateTo({
									url: '/pages/pay-result/index?status=fail',
								});
							}
						}
					},
					fail: (e) => {
						uni.hideLoading()
						console.log(e);
						uni.navigateTo({
							url: '/pages/pay-result/index?status=fail',
						});
					},
					complete: () => {
						uni.hideLoading()
						console.log("支付结束");
					}
				})
			},

			// 关闭订单
			finishTransaction(transaction) {
				return new Promise((resolve, reject) => {
					this.iap.finishTransaction(transaction, (res) => {
						resolve(res);
					}, (err) => {
						reject(err);
					});
				});
			},

			// 获取未关闭的订单
			restoreCompletedTransactions(username) {
				return new Promise((resolve, reject) => {
					this.iap.restoreCompletedTransactions({
						manualFinishTransaction: true,
						username
					}, (res) => {
						resolve(res);
					}, (err) => {
						reject(err);
					})
				});
			},

			// 检查是否有未关闭的订单
			async restore() {
				uni.showLoading({});
				try {
					// 从苹果服务器检查未关闭的订单，可选根据 username 过滤，和调用支付时透传的值一致
					const transactions = await this.restoreCompletedTransactions({
						username: this.userNumber
					});
					if (!transactions.length) {
						return;
					}
					for (let key in transactions) {
						let transaction = transactions[key];
						console.log(transaction);
						switch (transaction.transactionState) {
							case "1": // A successfully processed transaction.
								// 用户已付款，在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
								const paymentUsername = transaction.payment?.username || "";
								const username = paymentUsername.split("-")[0];
								const userNo = paymentUsername.split("-")[0];
								// 查找商品价格
								const product = this.vipRuleList.find(
									item => item.productid === transaction.payment?.productid
								);
								let res = await validateApplePayment({
									username: username,
									orderNo: userNo,
									payment: product?.price,
									transactionIdentifier: transaction.transactionIdentifier,
									transactionReceipt: transaction.transactionReceipt,
									payTime: transaction.transactionDate
								});
								// 关闭订单
								if (res.code == 200) {
									await this.finishTransaction(transaction);
								}
								break;
							case "2": // A failed transaction.
								// 关闭未支付的订单
								await this.finishTransaction(transaction);
								break;
							default:
								break;
						}
					}
				} catch (e) {
					console.log(e);
				} finally {
					uni.hideLoading();
				}
			},

			async applePay() {
				try {
					uni.showLoading({})
					let data = {
						payAmount: this.payAmount,
						productType: 1, // 会员
						mealId: this.versionMealList[this.selected - 1].mealId,
						platform: 4 // 苹果支付
					}
					let createOrderResponse = await createOrder(data);
					if (createOrderResponse.code != 200) {
						uni.showModal({
							content: '创建订单失败',
							showCancel: false
						});
						uni.hideLoading()
						return;
					}
					data = createOrderResponse.data;
					uni.hideLoading();
					this.requestPayment(data.orderNo)
				} catch (e) {
					console.log(e);
				}
			},
			
			async wxPay() {
				uni.showLoading({
					title: '加载中',
				});
				var data = {
					payAmount: this.payAmount,
					productType: 1, // 会员
					mealId: this.versionMealList[this.selected - 1].mealId
				};
				let createOrderResponse = await createOrder(data);
				if (createOrderResponse.code != 200) {
					uni.showToast({
						title: '创建订单失败',
						icon: 'error'
					});
					return;
				}
				data = createOrderResponse.data;
				data.platform = 3;
				data.appId = "wxb5e67df0de487557";
				let activePayResponse = await activePay(data);
				if (activePayResponse.code != 200) {
					uni.showToast({
						title: '订单激活失败',
						icon: 'error'
					});
					return;
				}
				data = {
					prepayId: activePayResponse.data,
					type: "uni"
				}
				let appConfigResponse = await appPayConfig(data);
				if (appConfigResponse.code != 200) {
					uni.showToast({
						title: '支付配置异常',
						icon: 'error'
					});
					return;
				}
				console.log(appConfigResponse);
				uni.hideLoading();
				let wxPayOrderInfo = {
					"appid": "wxb5e67df0de487557", // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
					"noncestr": appConfigResponse.data.nonceStr, // 随机字符串
					"package": "Sign=WXPay", // 固定值
					"partnerid": "**********", // 微信支付商户号
					"prepayid": activePayResponse.data, // 统一下单订单号
					"timestamp": Number(appConfigResponse.data.timeStamp), // 时间戳（单位：秒）
					"sign": appConfigResponse.data.paySign // 签名，这里用的 MD5/RSA 签名
				};
				uni.requestPayment({
					provider: 'wxpay',
					orderInfo: wxPayOrderInfo,
					success: function(res) {
						uni.navigateTo({
							url: '/pages/pay-result/index?status=success',
						});
					},
					fail: function(res) {
						console.log(res);
						uni.navigateTo({
							url: '/pages/pay-result/index?status=fail',
						});
					},
					complete: function(res) {
						
					},
				});
			},

			async openVersion() {
				if (this.platform == 'ios' || this.platform == 'mac') {
					// 苹果支付
					for (let index in this.vipRuleList) {
						if (this.vipRuleList[index].price == this.payAmount) {
							this.targetVipRule = this.vipRuleList[index];
							this.applePay(this.targetVipRule);
						}
					}
				} else {
					// 微信支付
					this.wxPay();
				}
			},
		},

		onShow() {
			setTimeout(() => {
				if (this.iap) {
					this.restore();
				}
			}, 1000)
		},

		onLoad(options) {
			const tokenExist = tokenStatus();
			if (!tokenExist) {
				uni.navigateTo({
					url: '/pages/login/index?url=/pages/version/index',
				})
			} else {
				let userNumber = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userNumber : '';
				this.userNumber = userNumber;
				this.getChannels();
				this.getVersionMeals({});
			}

		}
	}
</script>

<style lang="scss" scoped>
	.container {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		width: calc(750rpx - 56rpx);
		padding: 0 28rpx;
	}

	.version-info {
		width: 100%;
		margin-top: 20rpx;
		height: 100%;
		position: relative;
	}

	.version-title {
		display: flex;
		padding: 0 20rpx;
		font-size: 36rpx;
		align-items: center;
	}

	.version-title .activity {
		font-size: 48rpx;
		font-weight: 600;

	}

	.version-title .version-activity {
		display: flex;
		align-items: center;
		text-decoration: underline;
	}

	.version-title .version-activity image {
		width: 48rpx;
		height: 48rpx;
	}

	.version-title .version-activity .desc {
		font-size: 28rpx;
		font-weight: normal;
		text-decoration-line: #000;
	}

	.version-title .version-title-item {
		position: relative;
		margin-right: 86rpx;
	}

	.version-title .version-title-item .line {
		position: absolute;
		background-color: #F7CE46;
		width: 180rpx;
		height: 20rpx;
		border-radius: 8rpx;
		bottom: 20rpx;
		left: -15rpx;
		z-index: -10;
	}

	.version-desc {
		margin-top: 16rpx;
		font-size: 28rpx;
		margin-left: 20rpx;
	}

	.price-wrapper {
		display: flex;
		margin-top: 48rpx;
		justify-content: space-between;
	}

	.price-wrapper .price-card {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 144rpx;
		border-radius: 20rpx;
		border: 4rpx solid #ECECEC;
		background: #F6F6F6;
		padding: 18rpx 28rpx 0rpx 28rpx;
	}

	.price-wrapper .active {
		border-color: #46B4B1 !important;
		background-color: #EAF8F7 !important;
		color: #46B4B1 !important;
		font-weight: bold;
	}

	.price-wrapper .price-card .period {
		color: #787878;
		font-size: 28rpx;
		line-height: 56rpx;
	}

	.price-wrapper .price-card .price {
		margin-top: 8rpx;
		color: #333;
		font-size: 40rpx;
		line-height: 56rpx;
	}

	.price-wrapper .price-card .desc {
		margin-top: 26rpx;
		color: #787878;
		font-size: 24rpx;
		line-height: 56rpx;
		text-decoration: line-through;
	}

	.un-suport {
		color: #7E7E7E;
		text-align: center;
		font-size: 24rpx;
	}

	.un-suport .icon {
		width: 40rpx;
		height: 40rpx;
	}

	.about-version {
		width: 100%;
		height: 460rpx;
		margin-top: 28rpx;
	}

	.version-function-info {
		display: flex;
		flex-direction: column;
		padding: 16rpx;
	}

	.version-function-info .header {
		display: flex;
		justify-content: space-between;
		font-size: 28rpx;
		color: $uni-text-color;
		width: 100%;
	}



	.version-function-info .header view {
		text-align: center;
		width: 20%;
	}

	.version-function-info .header :nth-child(1) {
		display: flex;
		justify-content: flex-start;
		width: 40%;
	}

	.version-function-info .function-item {
		font-size: 24rpx;
		display: flex;
		align-items: center;
	}

	.version-function-info .function-item .desc {
		width: 40%;
	}

	.version-function-info .function-item .version-type {
		width: 20%;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $uni-text-color;
	}

	.version-function-info .function-item .version-type .function-state {
		width: 32rpx;
		height: 32rpx;
	}

	.version-function-info .function-item :nth-child(1) {
		justify-content: flex-start;
		color: #000;
	}

	.bottom-line {
		width: 100%;
		height: 1rpx;
		background-color: #D8D8D8;
	}

	.oper-area {
		position: fixed;
		bottom: 32rpx;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.button-version {
		width: 348rpx;
		background: #46B4B1;
		border-radius: 40rpx;
		color: #FFF;
		font-size: 32rpx;
		padding: 12rpx 80rpx;
		display: flex;
		justify-content: space-around;
		margin-top: 24rpx;
		transition: background 0.2s ease, transform 0.2s ease;
	}
	
	.button-version:active {
	    background: #3A9A97;
	    transform: scale(0.95);
	}

	.server-desc {
		color: #BFBFBF;
		font-size: 20rpx;
		text-decoration-line: underline;
	}
</style>