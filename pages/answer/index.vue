<template>
	<load v-if="loadingFlag"></load>
	<view class="page-body">
		<view class="questions">
			<view class="section-list">
				<view @click="selectQuestion(item.questionIndex)" class="question-section"
					v-for="(item, index) in questionList" v-key="index">
					<view class="title">
						第{{item.questionIndex}}题
					</view>
					<view v-if="questionIndex == item.questionIndex" class="show_bar">
					</view>
				</view>
			</view>
			<view class="question-context" @click="handleClickItem">
				<view class="title">
					<rich-text :nodes="questionContent">
					</rich-text>
				</view>
			</view>
		</view>
		<view class="answer-title">
			<view class="left-content">
				<view class="left-icon">
				</view>
				<view class="text-wrapper">
					<view class="title">
						我的答案
					</view>
				</view>
			</view>

			<view class="material-detail" @click="handleClickMaterials">
				<view class="tool">
					原文
				</view>
			</view>
		</view>
		<view class="answer-list">
			<view v-for="(item, index) in datas">
				<view class="answer-context">
					<view class="delete" @click="showSubmitDialog(item.answerId)">
						<image src="@/static/images/cancel.png" />
					</view>
					<view class="content">
						<rich-text :nodes="item.content" />
					</view>
					<view class="footer">
						<view class="content-length">（{{item.length}}字）</view>
						<view class="date-time">{{item.createTime}}</view>
					</view>
				</view>
			</view>
		</view>
		<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>
	</view>
</template>

<script>
	import {
		getQuestionList,
		getUserAnswerList,
		deleteUserAnswer
	} from '@/api/index';
	import {
		tokenStatus
	} from '@/utils/util';
	export default {
		data() {
			return {
				paperId: null,
				questionList: [],
				userAnswerList: [],
				loadingFlag: true,
				pageNum: 1,
				pageSize: 1000,
				datas: [],
				questionIndex: 1,
				questionId: '',
				questionContent: '',
				id: '',
				submitDialog: false,
				dialogContent: "此操作执行后将无法撤回"
			}
		},
		methods: {
			handleClickItem() {
				uni.navigateTo({
					url: '/pages/detail/index?paperId=' + this.paperId + '&questionIndex=' + this.questionIndex,
				})
			},

			selectQuestion(index) {
				var questionId = this.questionList[index - 1].questionId;
				this.class_name = "show_bar";
				this.questionIndex = index;
				this.questionId = questionId;
				this.handleQuestionShow();
				let filter = {
					questionId: questionId,
					userId: this.userId
				};
				this.selectUserAnswerDetail(filter);
			},

			selectQuestionList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getQuestionList(filter).then(res => {
					this.questionList = res.rows;
					this.handleQuestionShow();
					for (let key in this.questionList) {
						let item = this.questionList[key];
						if (item.questionIndex == this.questionIndex) {
							this.questionId = item.questionId;
							var filter = {
								questionId: item.questionId,
								userId: this.userId
							};
							this.selectUserAnswerDetail(filter);
						}
					}
				}).catch(err => {
					console.log(err)
				});
			},

			handleQuestionShow() {
				var question = this.questionList[this.questionIndex - 1];
				this.questionContent = question.content;
			},

			handleClickMaterials() {
				uni.navigateTo({
					url: '/pages/detail/index?activeTab=material&paperId=' + this.paperId,
				})
			},

			selectUserAnswerDetail(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getUserAnswerList(filter).then(res => {
					var dataList = res.rows;
					var datas = [];
					for (let i in dataList) {
						let data = {};
						let content = dataList[i].content;
						data.answerId = dataList[i].answerId;
						data.content = content;
						data.createTime = dataList[i].createTime;
						data.length = content.replace(/<[^>]*>/g, '').length;
						datas.push(data);
					}
					this.datas = datas;
					this.userAnswerList = dataList;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},

			showSubmitDialog(id) {
				this.id = id;
				this.submitDialog = true;
			},

			cancelSubmit() {
				this.submitDialog = false;
			},

			confirmSubmit() {
				const tokenExist = tokenStatus();
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/answer/index',
					})
				} else {
					deleteUserAnswer(this.id).then(res => {
						let filter = {
							questionId: this.questionId,
							userId: this.userId
						};
						this.selectUserAnswerDetail(filter);
						this.submitDialog = false;
					}).catch(err => {
						console.log(err)
					});
				}
			},
		},
		onLoad(options) {
			let userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
			let paperId = options.paperId;
			let filter = {
				paperId: paperId
			}
			this.paperId = paperId;
			this.userId = userId;
			this.selectQuestionList(filter);
		},
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.questions {
		margin-top: 20rpx;
		width: 100%;
		position: relative;
	}

	.questions .section-list {
		display: flex;
		justify-content: space-between;
		align-items: center;
		overflow-x: scroll;
		padding-bottom: 16rpx;	
	}

	.questions .section-list::-webkit-scrollbar {
		display: none;
	}

	.questions .section-list .question-section {
		display: flex;
		flex-direction: column;
	}

	.questions .section-list .question-section .title {
		display: flex;
		padding: 10rpx 20rpx;
		height: 35rpx;
		flex-direction: row;
		justify-content: center;
		color: #46B4B1;
		font-size: 28rpx;
		width: 100rpx;
	}

	.questions .section-list .question-section .show_bar {
		display: flex;
		height: 4rpx;
		border-radius: 20rpx;
		background: $uni-color-primary;
		width: 40%;
		margin: 0 30%;
	}

	.questions .section-list .question-section .hide_bar {
		display: none;
		height: 4rpx;
		border-radius: 20rpx;
		background: $uni-color-primary;
		width: 40%;
		margin: 0 30%;
	}

	.question-context {
		margin-top: 16rpx;
		height: auto;
		padding: 12rpx 24rpx;
		overflow-y: scroll;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
	}

	.question-context .title {
		display: flex;
		flex-direction: column;
		justify-content: center;
		font-size: 28rpx;
	}

	.answer-title {
		margin-top: 20rpx;
		width: 100%;
		display: flex;
		position: relative;
		justify-content: space-between;
		align-items: center;
	}

	.answer-title .left-content {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.answer-title .left-content .left-icon {
		display: inline-block;
		position: relative;
		width: 16rpx;
		height: 56rpx;
		border-radius: 20rpx;
		background: #46B4B1;
	}

	.answer-title .left-content .title {
		display: flex;
		position: relative;
		left: 20rpx;
		color: var(--unnamed, #333);
		text-align: center;
	}

	.answer-title .material-detail {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.answer-title .tool {
		color: #46B4B1;
		font-size: 28rpx;
	}

	.answer-title .right-icon image {
		width: 48rpx;
		height: 48rpx;
	}

	.answer-list {
		width: calc(100%);
		flex: 1;
		height: 1px;
		overflow-y: scroll;
		margin-bottom: 20rpx;
	}

	.answer-context {
		position: relative;
		word-wrap: break-word;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.answer-context .content {
		padding: 24rpx;
		padding-right: 48rpx;
		max-height: 400rpx;
		overflow-y: scroll;
		font-size: 28rpx;
		letter-spacing: 1rpx;
	}

	.answer-context .delete image {
		width: 44rpx;
		height: 44rpx;
		position: absolute;
		right: 0rpx;
		top: 0rpx;
	}

	.answer-context .footer {
		display: flex;
		justify-content: space-between;
	}

	.answer-context .footer .content-length {
		position: relative;
		display: inline-block;
		margin-bottom: 20rpx;
		color: #46B4B1;
		text-align: center;
		font-size: 24rpx;
		margin-left: 20rpx;
	}

	.answer-context .footer .date-time {
		position: relative;
		display: inline-block;
		margin-bottom: 20rpx;
		color: #46B4B1;
		text-align: center;
		font-size: 24rpx;
		margin-right: 20rpx;
	}
</style>