<template>
	<view class="page-body">
		<view class="page-body">
			<view class="icon">
				<image src="@/static/images/default-user.png" />
			</view>
			<view class="desc">使用当前账号登陆囊中论</view>
			<view class="confirm" @click="confirm">确认</view>
			<view class="cancel" @click="cancel">取消</view>
		</view>
	</view>
</template>

<script>
	import {
		updateScanCodeStatus
	} from '@/api/index';
	export default {
		data() {
			return {
				nextUrl: "/pages/mime/index",
				scene: null,
			}
		},
		methods: {
			confirm() {
				// 判断用户是否已登录
				const tokenExist = tokenStatus();
				const status = '2'; // 已确认
				if (!tokenExist) {
					// uni.getUserProfile({
					//   desc: '用于完善个人资料',
					//   success: res => {
					//     util.toLogin(this.nextUrl);
					//     setTimeout(() => {
					//       this.updateScanCode(this.scene, status);
					//     }, 2000);
					//   }
					// });
				} else {
					uni.reLaunch({
						url: this.nextUrl
					});
					this.updateScanCode(this.scene, status);
				}
			},

			cancel() {

			},

			updateScanCode(scene, status) {
				let sysUser = uni.getStorageSync('sys_user');
				let token = uni.getStorageSync('auth_token');
				updateScanCodeStatus({
					userNumber: sysUser.userNumber,
					scene: scene,
					status: status,
					token: token,
					isBind: sysUser.phone ? '1' : '0'
				}).then(res => {}).catch(err => {
					console.log(err)
				});
			},
		},

		onLoad(options) {
			const scene = decodeURIComponent(options.scene);
			this.scene = scene;
			// 已扫描
			const status = '1';
			this.updateScanCode(this.scene, status);
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.icon {
		margin-top: 128rpx;
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		border: 1px solid #84C7C3;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.icon image {
		width: 64rpx;
		height: 64rpx;
	}

	.desc {
		margin-top: 64rpx;
		font-size: 34rpx;
		color: #666;
	}

	.confirm {
		margin-top: 128rpx;
		display: flex;
		width: 656rpx;
		height: 96rpx;
		border-radius: 16rpx;
		background: #84C7C3;
		align-items: center;
		justify-content: center;
		position: relative;
		color: #FFFEFE;
		font-size: 40rpx;
	}

	.cancel {
		display: flex;
		margin-top: 32rpx;
		width: 656rpx;
		height: 96rpx;
		border-radius: 16rpx;
		align-items: center;
		justify-content: center;
		position: relative;
		color: #333;
		border: 2rpx solid #46B4B1;
		font-size: 40rpx;
	}
</style>