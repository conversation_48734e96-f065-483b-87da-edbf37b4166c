<template>
	<view class="page-body">
		<view class="view">
			<image src="@/static/images/notfound.png" />
		</view>
		<view class="desc">
			此功能还在快马加鞭上架中，耐心等待~
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {

		},
		onLoad(options) {

		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.view image {
		width: 520rpx;
		height: 640rpx;
		border-radius: 50%;
	}

	.desc {
		display: flex;
		height: 110rpx;
		flex-direction: column;
		justify-content: center;
		text-align: center;
		font-size: 32rpx;
	}
</style>