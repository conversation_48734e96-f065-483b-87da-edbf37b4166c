<template>
	<view class="container">
		<view class="page-body">
			<view class="app_bar">
				<view @click="selectMoudle(index)" class="moudle" :class="{'active': moudleIndex === index}" v-for="(item, index) in moudleList" :key="item.index">
					<text class="title">{{item["title"]}}</text>
					<view class="show_bar" v-if="moudleIndex == index"></view>
				</view>
			</view>
			<image class="background" src="@/static/images/background.png"></image>
			<view class="function">
				<image class="search" src="@/static/images/search-paper.png" @click="handleClickSearch()"></image>
				<image class="material" src="@/static/images/answer-add.png" @click="handleClickAddAnswer()"></image>
			</view>
			<view class="function-area">
				<view class="function-item">
					<image class="item-image" src="@/static/images/function-item-1.png" @click="handleAnswerHistory">
					</image>
					<text class="desc">我的答案库</text>
				</view>
				<view class="function-item">
					<image class="item-image" src="@/static/images/function-item-2.png" @click="handleCollect">
					</image>
					<text class="desc">收藏夹</text>
				</view>
				<view class="function-item">
					<image class="item-image" src="@/static/images/function-item-3.png" @click="handleNote">
					</image>
					<text class="desc">随手记</text>
				</view>
				<view class="function-item">
					<image class="item-image" src="@/static/images/function-item-4.png" @click="handleZt">
					</image>
					<text class="desc">真题字帖</text>
				</view>
			</view>
			<view class="version">
				<image class="version-background" src="@/static/images/version-background.png"></image>
				<image class="version-button" src="@/static/images/version-button.png" @click="openVersion()"></image>
			</view>
			<!-- <view class="line-area">
				<image class="target-line" src="@/static/images/target-line.png"></image>
				<view class="line-title">
					<view class="left-icon">
					</view>
					<view class="title">
						备考指南针
					</view>
				</view>
				<view class="line-function">
					<view class="ccq">
						<image class="font-4-img" src="@/static/images/ccq-1.png"></image>
						<image class="font-4-img" src="@/static/images/ccq-2.png" style="margin-right: 0rpx;"></image>
					</view>
					<view class="gjq">
						<image class="font-7-img" src="@/static/images/gjq-1.png"></image>
						<view class="flex">
							<image class="font-4-img" src="@/static/images/gjq-2.png"></image>
							<image class="font-5-img" src="@/static/images/gjq-3.png"></image>
						</view>
					</view>
					<view class="hsq">
						<image class="font-5-img" src="@/static/images/hsq-1.png"></image>
						<view class="flex">
							<image class="font-4-img" src="@/static/images/hsq-2.png"></image>
							<image class="font-4-img" src="@/static/images/hsq-3.png"></image>
						</view>
		
					</view>
				</view>
			</view> -->
			<activity @confirm="confirmActivity" v-if="userActivityVersion"></activity>
		</view>
	</view>
</template>

<script>
	import {
		userActivityPermission,
		insertUserVersion
	} from '@/api/index';
	import {
		tokenStatus,
		getUserData,
		adStatus
	} from '@/utils/util';
	import Activity from '@/components/activity';
	import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update'
	export default {
		data() {
			return {
				moudleIndex: 0,
				moudleList: [
					{
						title: "公务员",
						index: 0
					},
					{
						title: "事业单位",
						index: 1
					},
				],
				adStatus: true,
				operType: 1,
				userActivityVersion: null,
			}
		},
		
		components: {
			'activity': Activity
		},
		onReady() {
			checkUpdate();
		},
		onShow() {
			this.moudleIndex = uni.getStorageSync("data_type") ? uni.getStorageSync("data_type") : 0;
			const pages = getCurrentPages();
			const page = pages[pages.length - 1];
			this.isSupport = !this.isSupport;
			const tokenExist = tokenStatus();
			if (!tokenExist) {
				uni.navigateTo({
					url: "/pages/login/index?url=/pages/home/<USER>"
				})
			} else {
				getUserData();
				this.adStatus = adStatus();
				this.handleActivity();
			}
		},
		methods: {
			selectMoudle(index) {
				this.moudleIndex = index;
				uni.setStorageSync("data_type",index);
			},
			
			handleClickSearch() {
				uni.navigateTo({
					url: "/pages/area/index"
				})
			},
			
			handleClickAddAnswer() {
				uni.navigateTo({
					url: "/pages/answer-add/index"
				})
			},
			
			handleAnswerHistory() {
				const tokenExist = tokenStatus();
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/mime/index',
					})
				} else {
					uni.navigateTo({
						url: '/pages/answer-history/index',
					})
				}
			},
			
			handleCollect() {
				const tokenExist = tokenStatus();
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/mime/index',
					})
				} else {
					uni.navigateTo({
						url: '/pages/paper-collect/index',
					})
				}
			},
			
			handleNote() {
				const tokenExist = tokenStatus();
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/mime/index',
					})
				} else {
					uni.navigateTo({
						url: '/pages/note/index',
					})
				}
			},
			
			handleZt() {
				const tokenExist = tokenStatus();
				if(!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/zt/index',
					})
				} else {
					uni.navigateTo({
						url: '/pages/zt/index',
					})
				}
			},
			
			openVersion() {
				uni.navigateTo({
					url: "/pages/version/index"
				})
			},
			
			handleActivity(){
				let params = {
					startTime : "2025-01-28 00:00:00",
					endTime : "2025-02-04 23:59:59",
					period : 15
				}
				userActivityPermission(params).then(res=>{
					if(res.data) {
						this.userActivityVersion = res.data;
					}
				});
			},
			confirmActivity() {
				insertUserVersion(this.userActivityVersion).then(res => {
					uni.showToast({
						title: '领取成功',
						icon: "success"
					});
					uni.reLaunch({
						url: '/pages/mime/index',
					})
				}).catch(err => {
					console.log(err)
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.app_bar {
		margin-top: var(--status-bar-height);
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		height: 108rpx;
		align-items: center;
		.moudle {
			margin-right: 54rpx;
			.title {
				font-size: 28rpx;
				color: $uni-text-color;
				margin-bottom: 14rpx;
				transition: all 0.3s ease;
			}
			&.active .title {
				font-size: 32rpx;
				font-weight: bold;
			}
			.show_bar {
				display: flex;
				height: 8rpx;
				border-radius: 20rpx;
				background: $uni-color-primary;
				width: 80%;
				margin: 0 10%;
			}
		}
	}
	
	.container {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		width: calc(750rpx - 56rpx);
		padding: 0 28rpx;
	}

	.background {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: -1;
	}

	.function {
		display: flex;
		flex-direction: row;
		width: 100%;
		justify-content: space-between;
	}

	.function .search {
		height: 160rpx;
		width: 340rpx;
	}

	.function .material {
		height: 160rpx;
		width: 340rpx;
	}

	.function-area {
		height: 170rpx;
		margin-top: 20rpx;
		background-color: $uni-bg-color;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		width: 100%;
	}

	.function-area .function-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.function-area .function-item .item-image {
		width: 128rpx;
		height: 128rpx;
	}

	.function-area .function-item .desc {
		color: $uni-text-color;
		font-size: $uni-font-size-sm;
		position: relative;
		top: -10rpx;
	}

	.version {
		width: 100%;
		margin-top: 20rpx;
		position: relative;
	}

	.version .version-background {
		width: 100%;
		height: 104rpx;

	}

	.version .version-button {
		width: 174rpx;
		height: 48rpx;
		position: absolute;
		top: 28rpx;
		right: 18rpx;
	}

	.line-area {
		position: relative;
		width: 100%;
		height: 100%;
		margin-top: 20rpx;
	}

	.target-line {
		width: 100%;
		height: 830rpx;
		left: 0;
		z-index: -1;
		position: absolute;
	}

	.line-title {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.line-title .left-icon {
		position: relative;
		width: 16rpx;
		height: 56rpx;
		flex-shrink: 0;
		border-radius: 20rpx;
		background: $uni-color-primary;
	}

	.line-title .title {
		margin-left: 10rpx;
	}

	.line-area .line-function {

		display: flex;
		flex-direction: column;
		justify-content: space-around;
		height: 90%;
	}

	.line-function .ccq,
	.hsq,
	.gjq {
		display: flex;
		flex-direction: column;
	}

	.line-function .ccq {
		flex-direction: row;
		justify-content: flex-end;
	}

	.line-function .gjq {}

	.line-function .hsq {
		margin-bottom: 120rpx;
	}

	.line-function .gjq .font-7-img {
		margin-left: 96rpx;
	}

	.line-function .gjq .flex {
		display: flex;
		flex-direction: row;
	}

	.line-function .hsq .font-5-img {
		margin-right: 96rpx;
		margin-left: auto;
	}

	.line-function .hsq .flex {
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
	}

	.line-function .font-4-img,
	.line-function .font-5-img {
		width: 192rpx;
		height: 68rpx;
		margin: 17rpx;
	}

	.line-function .font-7-img {
		width: 220rpx;
		height: 68rpx;
		margin: 17rpx;
	}
</style>
