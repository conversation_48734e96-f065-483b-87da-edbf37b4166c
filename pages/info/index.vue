<template>
	<view class="page-body">
		<view class="info">
			<view class="avatar">头像
				<view class="icon" @click="chooseAvatar">
						<image class="inner-wrapper" v-if="imgUrl" :src="imgUrl" />
						<image class="inner-wrapper" v-else src="@/static/images/default-user.png" />
				</view>
			</view>
			<view class="nickname">昵称
				<view class="input">
					<input ref="inputRef" type="text" @input="handleInputName" :placeholder="placeholder1" />
				</view>
			</view>
			<!-- <view class="password">密码
				<view class="input">
					<input :type="showPassword ? 'text' : 'password'" @input="handleInputPassword"
						:placeholder="placeholder2" :value="sysUser.password ? '******' : ''" />
				</view>
				<image v-if="showPassword" src="@/static/images/preview-open.png" @click="handleShowPassword" />
				<image v-else src="@/static/images/preview-close.png" @click="handleShowPassword" />
			</view> -->
			<!-- NZ2023181602 -->
			<view v-if="sysUser.userNumber == 'NZ202318160171' || sysUser.userNumber == 'NZ2023181602'"
				class="userNumber">囊友号
				<view class="input">
					<input type="text" @input="handleInputUserNumber" placeholder="请输入囊友号" value="" />
				</view>
				<!-- <picker bindchange="bindPickerChange" value="{{index}}" range="{{array}}">
					<view class="picker">
						时间：{{array[index]}}个月
					</view>
				</picker> -->
				<view class="submitVersion" @click="submitVersion">开通纯净版</view>
			</view>
		</view>
		<view class="form_btn_wrap">
			<view class="form-btn" @click="handleUnregister">
				注销账户
			</view>
			<view class="form-btn" @click="handleSave">
				保存修改
			</view>
		</view>
		<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>
	</view>
</template>

<script>
	import {
		updateUser,
		unregister,
		insertUserVersion,
		uploadMinioUrl
	} from '@/api/index';
	export default {
		data() {
			return {
				placeholder1: "请输入昵称",
				placeholder2: "请设置密码",
				sysUser: null,
				imgUrl: "",
				array: [1, 3, 6],
				index: 0,
				period: 0,
				password: '',
				showPassword: false,
				submitDialog: false,
				dialogContent: '账户注销后，与账户相关的所有数据会被清空并无法恢复!'
			}
		},
		methods: {
			chooseAvatar() {
				let that = this;
				uni.chooseImage({
					count: 1,
					mediaType: ['image', ],
					sourceType: ['album', 'camera'],
					maxDuration: 30,
					camera: 'back',
					success(res) {
						let item = res.tempFiles[0];
						let tempFilePath = item.path;
						that.imgUrl = tempFilePath;
						let tokenKey = "auth_token";
						let token = uni.getStorageSync(tokenKey);
						uni.uploadFile({
							url: uploadMinioUrl,
							filePath: tempFilePath,
							name: 'file',
							formData: {},
							header: {
								'Authorization': token
							},
							success: res => {
								let data = JSON.parse(res.data);
								let imgUrl = data.url;
								let sysUser = that.sysUser;
								sysUser.avatar = imgUrl;
								that.sysUser = sysUser;
							},
							fail: err => {
								console.log(err);
							}
						})
					}
				})
			},

			handleInputName(e) {
				let sysUser = this.sysUser;
				sysUser.nickName = e.detail.value;
				this.sysUser = sysUser;
			},

			handleInputPassword(e) {
				this.password = e.detail.value;
			},

			handleShowPassword() {
				this.showPassword = !this.showPassword;
			},

			handleInputUserNumber(e) {
				let userNumber = e.detail.value.replace(/\s/g, '');
				this.userNumber = userNumber;
			},

			bindPickerChange(e) {
				this.index = e.detail.value;
			},
			
			handleUnregister() {
				this.submitDialog = true;
			},
			
			cancelSubmit() {
				this.submitDialog = false;
			},
			
			confirmSubmit() {
				unregister().then(res => {
					if (res.code == 200) { 
						uni.showToast({
							title: '注销成功',
							icon: 'success'
						});
						uni.reLaunch({
							url: "/pages/login/index"
						})
					} else {
						uni.showToast({
							title: '注销失败',
							icon: 'error'
						});
					}
				}).catch(err => {
					console.log(err)
				})
			},

			submitVersion() {
				let period = this.array[this.index];
				let currentDate = new Date();
				currentDate.setMonth(currentDate.getMonth() + period);
				let data = {
					user: {
						userNumber: this.userNumber,
					},
					version: {
						code: 'clean'
					},
					expireTime: currentDate
				}
				insertUserVersion(data).then(res => {
					uni.showToast({
						title: '开通成功',
						icon: 'success'
					});
				}).catch(err => {
					console.log(err)
				});
			},

			handleSave() {
				let nickname = this.sysUser.nickName;
				if (nickname.length > 10) {
					uni.showToast({
						title: '昵称过长',
						icon: 'error'
					});
					return;
				}
				if (nickname.length == 0) {
					uni.showToast({
						title: '昵称不能为空',
						icon: 'error'
					});
					return;
				}
				if (this.password.length > 0 && this.password.length < 6) {
					uni.showToast({
						title: '密码长度太短',
						icon: 'error'
					});
					return;
				} else {
					let sysUser = this.sysUser;
					sysUser.password = this.password;
					this.sysUser = sysUser;
				}
				updateUser(this.sysUser).then(res => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					uni.setStorageSync('sys_user', this.sysUser);
					uni.reLaunch({
						url: "/pages/mime/index"
					})
				}).catch(err => {
					console.log(err);
				});
			},
		},
		onLoad(options) {
			let sysUser = uni.getStorageSync('sys_user');
			this.sysUser = sysUser;
			this.imgUrl = sysUser ? sysUser.avatar : '';
			this.$nextTick(() => {
				this.$refs.inputRef.value = sysUser.nickName;
			});
			
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
	}

	.info {
		width: 100%;
	}

	.info .avatar,
	.nickname,
	.password,
	.userNumber {
		display: flex;
		justify-content: space-between;
		height: 120rpx;
		display: flex;
		align-items: center;
		border-bottom: 0.5rpx solid #CFD1CF;
		padding: 0 40rpx;
		font-size: 28rpx;
	}

	.icon {
		width: 96rpx;
		height: 96rpx;
		fill: #CFD1CF;
		stroke-width: 12rpx;
		stroke: #F6F3F3;
		margin-right: 20rpx;
	}

	.icon .inner-wrapper {
		width: 72rpx;
		height: 72rpx;
	}

	.password image {
		width: 48rpx;
		height: 48rpx;
	}

	.nickname .input,
	.password .input {
		width: 70%;
		text-align: right;
		font-size: 28rpx;
	}

	.userNumber .input {
		width: 30%;
		text-align: right;
		border-bottom: 1px solid #46B4B1;
		color: #46B4B1;
		font-size: 28rpx;
	}

	.userNumber .submitVersion {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx;
		height: 60rpx;
		border-radius: 60rpx;
		background: #46B4B1;
		color: #FFF;
		text-align: center;
		padding: 0 20rpx;
		font-size: 28rpx;
	}


	.form_btn_wrap {
		margin-top: 56rpx;
		display: flex;
		justify-content: space-around;
		width: 100%;
	}
	
	.form_btn_wrap :first-child {
		background-color: #FFF;
		color:  $uni-color-error;
		border: 1px solid $uni-color-error;
	}

	.form-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 14rpx 36rpx;
		border-radius: 128rpx;
		background: #46B4B1;
		color: #FFF;
		text-align: center;
		font-size: 28rpx;
	}
</style>