<template>
	<page-meta :page-font-size="currentFontSize * 8/7+'px'" :root-font-size="currentFontSize * 8/7 +'px'" :key="metaKey"></page-meta>
	<load v-if="loadingFlag"></load>
	<fui-toast ref="toast"></fui-toast>
	<view class="page-body">
		<view class="full-answer-context" v-if="fullScreenExpand">
			<view class="content-wrapper">
				<view v-if="answerTab === 'answer'">
					<view v-for="(item, index) in answerList" :key="index">
						<view class="header">
							<view class="left">
								{{'某' + item.source.split("")[1]}}
								<!-- <view class="copy">
									<image src="@/static/images/copy.png" @click="handleCopy(item.content)" />
								</view> -->
								<!-- 添加单选按钮 -->
								<view class="radio-select" v-if="fromZt" @click.stop="selectAnswer(item, 'answer', index)">
									<view class="radio-button" :class="{'selected': selectedAnswerType === 'answer' && selectedAnswerIndex === index}"></view>
									<view class="radio-label">选择生成</view>
								</view>
							</view>
							<view class="right">
								<image src="@/static/images/un-support.png" v-if="answerActionSupportList[index].status == '0'" @click="action(['1',1,3,item.answerId])"/>
								<image src="@/static/images/support.png" v-if="answerActionSupportList[index].status == '1'" @click="action(['0',1,3,item.answerId])" />{{answerActionSupportList[index].count}}
								<image src="@/static/images/un-against.png" v-if="answerActionAgainstList[index].status == '0'" @click="action(['1',2,3,item.answerId])" />
								<image src="@/static/images/against.png" v-if="answerActionAgainstList[index].status == '1'" @click="action(['0',2,3,item.answerId])" />{{answerActionAgainstList[index].count}}
							</view>
						</view>
						<rich-text class="ql-editor" :nodes="item.content" v-if="item.source !== '中公' || !adStatus">
						</rich-text>
						<view class="no-permission" v-if="item.source === '中公' && adStatus">
							<image src="@/static/images/no-permission.png"/>
							<view class="desc">非会员无权限查看中公答案！</view>
						</view>
					</view>
					<empty type="1" desc = '暂无机构答案!' :data = 'answerList'></empty>
				</view>
				<view v-if="answerTab === 'add'">
					<view v-if="!adStatus" v-for="(item, index) in userAddAnswerList"
						:key="index">
						<view class="header">
							<view class="left">{{item.source}}
								<view class="radio-select" v-if="fromZt" @click.stop="selectAnswer(item, 'answer', index)">
									<view class="radio-button" :class="{'selected': selectedAnswerType === 'answer' && selectedAnswerIndex === index}"></view>
									<view class="radio-label">选择生成</view>
								</view>	
							</view>
							<view class="right">
								<image src="@/static/images/un-support.png" v-if="addActionSupportList[index].status == '0'"
									@click="action(['1',1,4,item.addId])" />
								<image src="@/static/images/support.png" v-if="addActionSupportList[index].status == '1'"
									@click="action(['0',1,4,item.addId])" />
								{{addActionSupportList[index].count}}
								<image src="@/static/images/un-against.png" v-if="addActionAgainstList[index].status == '0'"
									@click="action(['1',2,4,item.addId])" />
								<image src="@/static/images/against.png" v-if="addActionAgainstList[index].status == '1'"
									@click="action(['0',2,4,item.addId])" />
								{{addActionAgainstList[index].count}}
							</view>
						</view>
						<rich-text :nodes="item.content">
						</rich-text>
					</view>
					<empty type="2" desc = '暂无网友补充答案!' :data = 'userAddAnswerList'></empty>
					<view class="no-permission" v-if="userAddAnswerList.length > 0 && adStatus">
						<image src="@/static/images/no-permission.png" />
						<view class="desc">非会员无权限查看网友补充答案！</view>
					</view>
				</view>
			</view>
		</view>
		<view class="not-full" v-else>
			<view class="questions">
				<view class="functions">
					<view class="function-buttons">
						<view class="button-item" :class="{'active': activeTab === 'paper'}" @click="switchTab('paper')">
							题目
						</view>
						<view class="button-item" :class="{'active': activeTab === 'material'}" @click="switchTab('material')">
							材料
						</view>
					</view>
					<view class="tool">
						<image src="@/static/images/font-size.png" @click="handleFontSize"></image>
					</view>
				</view>
				<view class="question-context" v-if="activeTab === 'paper'">
					<view class="section-list">
						<view @click="selectQuestion(item.questionIndex)" class="question-section"
							v-for="(item, index) in questionList" :key="index">
							<view class="title">
								第{{item.questionIndex}}题
							</view>
							<view v-if="questionIndex == item.questionIndex" class="show_bar"></view>
						</view>
					</view>
					<rich-text :nodes="questionContent"></rich-text>
				</view>
				<view class="material-context" v-if="activeTab === 'material'">
					<view class="section-list">
						<view @click="selectMaterial(item.materialIndex)" class="material-section"
							v-for="(item,index) in materialList" :key="index">
							<view class="title">
								材料{{item.materialIndex}}
							</view>
							<view v-if="materialIndex == item.materialIndex" class="show_bar">
							</view>
						</view>
					</view>
					<rich-text :nodes="materialContent">
					</rich-text>
				</view>
			</view>
			
			<view class="answers-area" v-if="activeTab === 'paper'">
				<view class="answer-types-wrapper">
					<view class="answer-types">
						<view class="button-item" :class="{'active': answerTab === 'answer'}" @click="switchAnswer('answer')">
							参考
						</view>
						<view class="button-item" :class="{'active': answerTab === 'add'}" @click="switchAnswer('add')">
							补充（{{userAddAnswerList.length > 99 ? '99+' : userAddAnswerList.length}}）
						</view>
						<view class="button-item" :class="{'active': answerTab === 'user'}" @click="switchAnswer('user')">
							我的
						</view>
					</view>
					<view  class="answer-tool" v-if="answerTab !== 'user'">
						<image src="@/static/images/expand.png" @click="handleAnswerFullScreen(answerTab)" />
					</view>
				</view>
				<view class="answer-context">
					<view class="answers" v-if="answerTab === 'answer'">
						<view class="content">
							<view v-for="(item, index) in answerList" :key="index">
								<view class="header">
									<view class="left">
										{{'某' + item.source.split("")[1]}}
										<!-- <view class="copy">
											<image src="@/static/images/copy.png" @click="handleCopy(item.content)" />
										</view> -->
										<view class="radio-select" v-if="fromZt" @click.stop="selectAnswer(item, 'answer', index)">
											<view class="radio-button" :class="{'selected': selectedAnswerType === 'answer' && selectedAnswerIndex === index}"></view>
											<view class="radio-label">选择生成</view>
											</view>
									</view>
									<view class="right">
										<image src="@/static/images/un-support.png" v-if="answerActionSupportList[index].status == '0'" @click="action(['1',1,3,item.answerId])" />
										<image src="@/static/images/support.png" v-if="answerActionSupportList[index].status == '1'" @click="action(['0',1,3,item.answerId])" />{{answerActionSupportList[index].count}}
										<image src="@/static/images/un-against.png" v-if="answerActionAgainstList[index].status == '0'" @click="action(['1',2,3,item.answerId])" />
										<image src="@/static/images/against.png" v-if="answerActionAgainstList[index].status == '1'" @click="action(['0',2,3,item.answerId])" />{{answerActionAgainstList[index].count}}
									</view>
								</view>
								<rich-text :nodes="item.content" v-if="item.source !== '中公' || !adStatus"></rich-text>
								<view class="no-permission" v-if="item.source === '中公' && adStatus">
									<image src="@/static/images/no-permission.png"/>
									<view class="desc">非会员无权限查看中公答案！</view>
								</view>
							</view>
							<empty type="1" desc = '暂无机构答案!' :data = 'answerList'></empty>
						</view>
					</view>
					<view class="user-add-answers" v-if="answerTab === 'add'">
							<view class="answer-title">
								<view class="oper">
									<view  @click="addAnswer" class="add">我要补充答案</view>
								</view>
							</view>
							<view class="content">
								<view v-if="!adStatus" v-for="(item, index) in userAddAnswerList" :key="index">
									<view class="header">
										<view class="left">{{item.source}}
										<view class="radio-select" v-if="fromZt" @click.stop="selectAnswer(item, 'answer', index)">
									<view class="radio-button" :class="{'selected': selectedAnswerType === 'answer' && selectedAnswerIndex === index}"></view>
											<view class="radio-label">选择生成</view>
									</view>	
										</view>
										<view class="right">
											<image src="@/static/images/un-support.png"
												v-if="addActionSupportList[index].status == '0'"
												@click="action(['1',1,4,item.addId])" />
											<image src="@/static/images/support.png"
												v-if="addActionSupportList[index].status == '1'"
												@click="action(['0',1,4,item.addId])" />
											{{addActionSupportList[index].count}}
											<image src="@/static/images/un-against.png"
												v-if="addActionAgainstList[index].status == '0'"
												@click="action(['1',2,4,item.addId])" />
											<image src="@/static/images/against.png"
												v-if="addActionAgainstList[index].status == '1'"
												@click="action(['0',2,4,item.addId])" />
											{{addActionAgainstList[index].count}}
										</view>
									</view>
									<rich-text :nodes="item.content">
									</rich-text>
								</view>
								<empty type="2" desc = '暂无网友补充答案!' :data = 'userAddAnswerList'></empty>
								<view class="no-permission" v-if="userAddAnswerList.length > 0 && adStatus">
									<image src="@/static/images/no-permission.png" />
									<view class="desc">非会员无权限查看网友补充答案！</view>
								</view>
							</view>
						</view>
						<view class="user-answer"  v-if="answerTab === 'user'">
							<view class="content">
								<richText ref="richText" :readOnly="readOnly" :placeholder="placeholder"
									@onEditorReady='onEditorReady' @bindinput='bindinput' @bindfocus="bindfocus"
									@ocrScanEvent='ocrScanEvent'>
								</richText>
								<view class="length">（{{userAnswerContentLenght}}字）</view>
							</view>
							<view class="answer-tool">
								<view class="commit-button" @click="showSubmitDialog('submit-user-answer')">
									上传至答案库
								</view>
							</view>
							<view id="bottom"></view>
						</view>
				</view>
				<view class="define-bar" v-if="defineBarShow">
					<view class="menu-item">
						<image src="@/static/images/un-star.png" v-if="paperActionTarget && paperActionTarget.status == '0'"
							@click="action(['1',3,1,paperActionTarget.targetId])" />
						<image src="@/static/images/star.png" v-if="paperActionTarget && paperActionTarget.status == '1'"
							@click="action(['0',3,1,paperActionTarget.targetId])" />
						<text>{{paperActionTarget && paperActionTarget.status == '0' ? '收藏试卷' : '取消收藏'}}</text>
					</view>
					<view class="menu-item" @click="hideModal2? showModalView('note') : hideModalView('note')">
						<image src="@/static/images/pencial.png" />
						<text>随手记</text>
					</view>
					<view class="menu-item" @click="hideModal? showModalView('history') : hideModalView('history')">
						<image src="@/static/images/history.png" />
						<text>历史答案</text>
					</view>
					<view class="menu-item" @click="hideModal1? showModalView('comment') : hideModalView('comment')">
						<image src="@/static/images/comments.png" />
						<text>发布想法</text>
					</view>
				</view>
			</view>
			
			<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>

			<view class="popup" v-if="!hideModal || !hideModal1 || !hideModal2">
				<view class="maskLayer"
					@click="hideModalView(!hideModal ? 'history' :(!hideModal1 ? 'comment' : 'note'))">
				</view>
				<view class='choose' :animation="animate">
					<view class="move-tag" @touchend="bindMoveCancel" @touchmove.stop.prevent="bindMoveEvent">
						<image src="@/static/images/drop-tag.png" />
					</view>
					<scroll-view scroll-y="scroll" class='list' :style="'height:' + modalHeight + 'rpx;'">
						<view v-for="(item, key) in datas" :key="key" class="context">
							<view class="delete" v-if="!hideModal"
								@click="showSubmitDialog('delete-answer', item.answerId)">
								<image src="@/static/images/cancel.png" />
							</view>
							<view class="delete" v-if="!hideModal2"
								@click="showSubmitDialog('delete-note',item.noteId)">
								<image src="@/static/images/cancel.png" />
							</view>
							<view v-if="!hideModal1" class="header">
								<view class="avatar">
									<image v-if="item.avatar" :src="item.avatar" />
									<image v-else src = "@/static/images/default-user.png" />
								</view>
								<view class="nickname">{{item.nickName ? item.nickName : '囊友'}}</view>
								<view class="delete-button" v-if="item.userId == userId"
									@click="showSubmitDialog('delete-comment', item.commentId)">删除此想法</view>
								<view class="create-time">{{item.createTime}}</view>
							</view>
							<view class="content">
								<rich-text :nodes="item.content" />
							</view>
							<view class="footer" v-if="!hideModal">
								<view class="content-length">（{{item.length}}字）</view>
								<view class="date-time">{{item.createTime}}</view>
							</view>
						</view>
						<view class="no-more" v-if="!hideModal && datas.length == 0">没有历史答案</view>
						<view class="no-more" v-if="!hideModal1 && datas.length == 0">没有答案评论</view>
						<view class="no-more" v-if="!hideModal2 && datas.length == 0">没有随手笔记</view>
					</scroll-view>
					<view class="input-area-wrapper">
						<view class="input-area" v-if="!hideModal1 || !hideModal2">
							<textarea ref="inputRef" cursor-spacing="120rpx" placeholder="请输入内容......" maxlength="-1" @input="bindTextInput"></textarea>
							<view class="commit-button" @click="showSubmitDialog('submit-input')">提交</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	let richText = null;
	import {
		getPaperList,
		getQuestionList,
		getMaterialList,
		getAnswerList,
		getUserAddAnswerList,
		getTargetsActionStatus,
		doAction,
		getUserNoteList,
		getUserAnswerList,
		getUserCommentList,
		insertUserAnswer,
		insertUserComment,
		insertUserNote,
		deleteUserAnswer,
		deleteUserComment,
		deleteUserNote,
		uploadMinioUrl
	} from '@/api/index';
	import {
		getUserData,
		adStatus,
		tokenStatus
	} from '@/utils/util';
	import {
		highLight
	} from '@/utils/filtrate';
	export default {
		data() {
			return {
				activeTab: 'paper',
				answerTab: 'answer',
				currentFontSize: 14,
				metaKey: 0,
				loadingFlag: true,
				paperId: null,
				scanContent: '',
				questionList: [],
				materialList: [],
				answerList: [],
				userAddAnswerList: [],
				fullScreenExpand: false,
				addActionSupportList: [],
				addActionAgainstList: [],
				answerActionSupportList: [],
				answerActionAgainstList: [],
				paperActionTarget: null,
				collectStatus: false,
				pageNum: 1,
				pageSize: 1000,
				barClass: "hide_bar",
				questionIndex: 1,
				materialIndex: 1,
				questionId: '',
				paperTitle: "",
				agencyIndex: 1,
				source: '粉笔',
				questionContent: '',
				materialContent: '',
				answerContent: '',
				userAnswerContentLenght: 0,
				content: '',
				userAnswerId: '',
				userCommentId: null,
				userNoteId: null,
				submitType: '',
				dialogContent: '',
				submitDialog: false,
				readOnly: false,
				adStatus: true,
				placeholder: "请输入",
				hideModal: true,
				hideModal1: true,
				hideModal2: true,
				defineBarShow: true,
				animate: {},
				screenWidth: 0,
				screenHeight: 0,
				modalHeight: 500,
				containerMove: true,
				dataType: uni.getStorageSync("data_type") ? uni.getStorageSync("data_type") : 0,
				fromZt: false,
				selectedAnswerType: null, // 'answer' 或 'add'
				selectedAnswerIndex: -1,  // 选中的答案索引
				selectedAnswerContent: null, // 选中的答案内容
			}
		},
		methods: {
			switchTab(tab) {
				this.activeTab = tab;
				if(tab === 'material') {
					this.defineBarShow = false;
				}
				if(tab === 'paper') {
					this.defineBarShow = true;
				}
			},
			
			handleFontSize() {
				uni.navigateTo({
					url: '/pages/setting/index' // 目标页面路径
				});
			},

			switchAnswer(tab) {
				this.answerTab = tab;
			},

			selectQuestion(index) {
				this.questionIndex = index;
				let questionId = null;
				this.questionList.forEach(item => {
					if (item.questionIndex == index) {
						questionId = item.questionId
					}
				});
				this.questionId = questionId;
				this.handleQuestionShow();
				let filter = {
					questionId: questionId
				};
				this.selectAnswerList(filter);
			},

			handleQuestionShow() {
				let question = null;
				this.questionList.forEach(item => {
					if (item.questionIndex == this.questionIndex) {
						question = item;
					}
				});
				this.questionContent = question.content;
			},

			handleAnswerShow() {
				let answers = [];
				for (let key in this.answerList) {
					let target = this.answerList[key];
					let content = target.content.toString();
					let result = highLight(content, '', 5);
					for (let key in result) {
						content = content.replace(new RegExp(result[key], "gm"),
							`<span style="color:#FF9C07;">${result[key]}</span>`)
					}
					target.content = content;
					answers.push(target);
				}
				this.answerList = answers;
				let params = {};
				params.targetIds  = '';
				for (let key in answers) {
					params.targetIds = params.targetIds + answers[key].answerId + ",";
				}
				params.targetIds = params.targetIds.slice(0, -1);
				params.targetType = 3;
				// 参考答案点赞数量
				params.actionType = 1;   
				getTargetsActionStatus(params).then(res=>{
					this.answerActionSupportList = res.data;
				});
				  // 参考答案点踩数量
				params.actionType = 2;
				getTargetsActionStatus(params).then(res=>{
					this.answerActionAgainstList = res.data;
				});
			},

			addAnswer() {
				uni.navigateTo({
					url: '/pages/answer-add/index?paperId=' + this.paperId + '&paperTitle=' + this.paperTitle +
						'&questionIndex=' + this.questionIndex,
				})
			},

			compareAnswer() {
				this.handleAnswerShow();
				let that = this;
				richText.editorCtx.getContents({
					success: function(res) {
						let result = highLight(that.answerContent, res.text, 5);
						if (result.length == 0) {
							let options = {
								text: '无五字以上重复内容'
							}
							that.$refs.toast.show(options);
						}
						for (let key in result) {
							that.answerContent = that.answerContent.replace(new RegExp(result[key], "gm"),
								`<span style="color:#FF9C07;">${result[key]}</span>`)
						}
					},
					fail: function(err) {
						console.log(err)
					}
				});
			},

			bindinput(e) {
				let value = e.value.detail.text.replace(/[(^*\n*)|(^*\r*)]/g, '');
				this.userAnswerContentLenght = value.length;
			},
			
			bindfocus(e) {
				this.metaKey +=1;
			},

			async showModalView(type) {
				this.modalHeight = 500;
				this.animation.translateY('0vh').step();
				var that = this;
				const tokenExist = tokenStatus();
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/detail/index',
					})
				} else {
					this.datas = [];
					let userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
					this.userId = userId;
					if (type == 'history') {
						let filter = {
							"params[paperId]": this.paperId,
							questionId: this.questionId,
							userId: userId
						};
						await this.selectUserAnswerDetail(filter);
						this.hideModal = false;
					} else if (type == "comment") {
						let filter = {
							targetId: this.questionId,
							targetType: 2,
						};
						await this.selectUserComment(filter);
						this.hideModal1 = false;
					} else if (type == 'note') {
						var filter = {
							targetId: this.questionId,
							targetType: 2,
							userId: userId
						};
						await this.selectUserNote(filter);
						this.hideModal2 = false;
					}
				}
			},

			showSubmitDialog(type, id) {
				if (type == 'submit-user-answer') {
					if (this.userAnswerContentLenght > 0) {
						this.submitType = 'submit-user-answer';
						this.dialogContent = "确认上传后，输入框内容将会清空。请确认已完成比对后，再进行上传";
						this.submitDialog = true;
					} else {
						uni.showToast({
							title: '未填写答案内容',
							icon: "error"
						});
					}
				} else if (type == 'delete-answer') {
					this.userAnswerId = id;
					this.submitType = 'delete-answer';
					this.dialogContent = "删除我的答案";
					this.submitDialog = true;
				} else if (type == 'delete-comment') {
					this.userCommentId = id;
					this.submitType = 'delete-comment';
					this.dialogContent = "删除我的评论";
					this.submitDialog = true;
				} else if (type == 'delete-note') {
					this.userNoteId = id;
					this.submitType = 'delete-note';
					this.dialogContent = "删除我的笔记";
					this.submitDialog = true;
				} else if (type == 'submit-input') {
					if (this.content.length > 0) {
						this.submitType = 'submit-input';
						// 评论
						if(!this.hideModal1) {
							this.dialogContent = "提交后的评论将会被大家看到，请确认是否提交？";
						}
						// 笔记
						if(!this.hideModal2) {
							this.dialogContent = "确认是否提交该笔记？";
						}
						this.submitDialog = true;
					} else {
						uni.showToast({
							title: '未填写内容',
							icon: "error"
						});
					}
				}
			},
			
			bindTextInput(e) {
				this.content = e.detail.value;
			},

			cancelSubmit() {
				this.submitDialog = false;
			},

			async confirmSubmit() {
				var that = this;
				const tokenExist = tokenStatus();
				var userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
				if (!tokenExist) {
					un.navigateTo({
						url: '/pages/login/index?url=/pages/detail/index',
					})
				} else {
					var submitType = this.submitType;
					if (submitType == 'submit-user-answer') {
						richText.editorCtx.getContents({
							success: function(res) {
								let data = {};
								data.content = res.html;
								data.userId = userId;
								data.questionId = that.questionId;
								insertUserAnswer(data).then(res => {
									that.submitDialog = false;
									richText.editorCtx.setContents({
										html: '',
									});
									that.userAnswerContentLenght = 0;
									uni.showToast({
										title: '提交成功',
										icon: 'success'
									});
								}).catch(err => {
									console.log(err)
								});
							},
							fail: function(err) {
								console.log(err)
							}
						});
					} else if (submitType == 'delete-answer') {
						deleteUserAnswer(this.userAnswerId).then(res => {
							let filter = {
								"params[paperId]": this.paperId,
								questionId: this.questionId,
								userId: this.userId
							};
							this.selectUserAnswerDetail(filter);
							this.submitDialog = false;
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}).catch(err => {
							console.log(err)
						});
					} else if (submitType == 'delete-comment') {
						deleteUserComment(this.userCommentId).then(res => {
							var filter = {
								targetId: this.questionId,
								targetType: 2,
							};
							this.selectUserComment(filter);
							this.submitDialog = false;
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}).catch(err => {
							console.log(err)
						});
					} else if (submitType == 'delete-note') {
						deleteUserNote(this.userNoteId).then(res => {
							var filter = {
								targetId: this.questionId,
								targetType: 2,
								userId: userId
							};
							this.selectUserNote(filter);
							this.submitDialog = false;
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}).catch(err => {
							console.log(err)
						});
					} else if (submitType == 'submit-input') {
						var content = this.content;
						var questionId = this.questionId;
						var data = {
							userId: userId,
							targetType: 2,
							targetId: questionId,
							content: content,
						}
						if (!this.hideModal1) {
							insertUserComment(data).then(res => {
								var filter = {
									targetId: this.questionId,
									targetType: 2,
								};
								this.selectUserComment(filter);
								this.submitDialog = false;
								this.$refs.inputRef.value = '';
								uni.showToast({
									title: '提交成功',
									icon: 'success'
								});
							}).catch(err => {
								console.log(err)
							});
						}
						if (!this.hideModal2) {
							insertUserNote(data).then(res => {
								var filter = {
									targetId: this.questionId,
									targetType: 2,
									userId: userId
								};
								this.selectUserNote(filter);
								this.submitDialog = false;
								this.$refs.inputRef.value = '';
								uni.showToast({
									title: '提交成功',
									icon: 'success'
								});
							}).catch(err => {
								console.log(err)
							});
						}
					}
				}
			},

			onEditorReady() {
				richText = this.$refs.richText; //获取组件实例
			},

			insertImageEvent() {
				uni.chooseImage({
					count: 1,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					success: res => {
						let item = res.tempFiles[0];
						let tempFilePath = item.path;
						let tokenKey = "auth_token";
						let token = uni.getStorageSync(tokenKey);
						uni.uploadFile({
							url: uploadMinioUrl,
							filePath: tempFilePath,
							name: 'file',
							formData: {},
							header: {
								'Authorization': token
							},
							success: res => {
								let data = JSON.parse(res.data);
								let imgUrl = data.url;
								richText.insertImageMethod(imgUrl).then(res => {}).catch(err => {
									console.log(err)
								});
							},
							fail: err => {
								console.log(err);
							}
						})
					}
				})
			},

			ocrScanEvent() {
				uni.chooseImage({
					count: 1,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					success: res => {
						let item = res.tempFiles[0];
						let tempFilePath = item.path;
						var tokenKey = "auth_token";
						let token = uni.getStorageSync(tokenKey);
						uni.uploadFile({
							url: uploadMinioUrl,
							filePath: tempFilePath,
							name: 'file',
							formData: {},
							header: {
								'Authorization': token
							},
							success: res => {
								let data = JSON.parse(res.data);
								var imgUrl = data.url;
								uni.navigateTo({
									url: '/pages/ocr/index?imgUrl=' + imgUrl +
										'&paperId=' +
										this.paperId + '&from=detail' +
										'&questionIndex=' + this.questionIndex
								})
							},
							fail: err => {
								console.log(err);
							}
						})
					}
				})
			},
			
			bindMoveEvent(e) {
			    var touchs = e.touches[0];
			    var pageY = touchs.pageY;
				let offset = 0;
				if(pageY * this.pix < 100) {
					// 拉到最高为止
					return;
				} else if(this.screenHeight - pageY * this.pix < 500) {
					// 拉到最低为止
					return;
				}
				if(!this.hideModal1 || !this.hideModal2) {
					// 随手记或者发布想法，预留输入栏
					offset = 230;
				} else {
					offset = 40;
				}
			    this.modalHeight = this.screenHeight - pageY * this.pix - offset;
			},
			
			bindMoveCancel(e) {
			    this.containerMove = true
			},

			hideModalView(type) {
				this.animation.translateY('100vh').step()
				this.animate = this.animation.export();
				setTimeout(() => {
					if (type == 'history') {
						this.hideModal = true;
					} else if (type == 'comment') {
						this.hideModal1 = true;
					} else if (type == 'note') {
						this.hideModal2 = true;
					}
				}, 600)
			},

			async selectUserNote(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				let res = await getUserNoteList(filter);
				let dataList = res.rows;
				let datas = [];
				for (let i in dataList) {
					let data = {};
					let content = dataList[i].content;
					data.noteId = dataList[i].noteId;
					data.content = content;
					data.createTime = dataList[i].createTime.split("T")[0];
					data.length = content.length;
					data.userId = dataList[i].userId;
					datas.push(data);
				}
				this.datas = datas;
				this.animate = this.animation.export();
			},

			async selectUserAnswerDetail(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				let res = await getUserAnswerList(filter);
				let dataList = res.rows;
				let datas = [];
				for (let i in dataList) {
					let data = {};
					let content = dataList[i].content;
					data.answerId = dataList[i].answerId;
					data.content = content;
					data.createTime = dataList[i].createTime;
					data.length = content.length;
					datas.push(data);
				}
				this.datas = datas;
				this.animate = this.animation.export();
			},

			async selectUserComment(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				let res = await getUserCommentList(filter);
				console.log(res);
				let dataList = res.rows;
				let datas = [];
				for (let i in dataList) {
					let data = {};
					let content = dataList[i].content;
					data.commentId = dataList[i].commentId;
					data.content = content;
					data.createTime = dataList[i].createTime.split("T")[0];
					data.length = content.length;
					if (dataList[i].user) {
						data.avatar = dataList[i].user.avatar;
						data.nickName = dataList[i].user.nickName;
					}
					data.userId = dataList[i].userId;
					datas.push(data);
				}
				this.datas = datas;
				this.animate = this.animation.export();
			},

			changeActionStatus(targetType, actionType, targetId, status) {
				  if(targetType == 3) {
					if(actionType == 1) {
						let answerActionSupportList = this.answerActionSupportList;
						let targetIndex = this.answerActionSupportList.findIndex(item => item.targetId === targetId);
						answerActionSupportList[targetIndex].status = status;
						answerActionSupportList[targetIndex].count = answerActionSupportList[targetIndex].count + (status == "1" ? 1 : -1);
						this.answerActionSupportList = answerActionSupportList;
					  }
					if(actionType == 2) {
						let answerActionAgainstList = this.answerActionAgainstList;
						let targetIndex = this.answerActionAgainstList.findIndex(item => item.targetId === targetId);
						answerActionAgainstList[targetIndex].status = status;
						answerActionAgainstList[targetIndex].count = answerActionAgainstList[targetIndex].count + (status == "1" ? 1 : -1);
						this.answerActionAgainstList = answerActionAgainstList;
					}
				}
				if(targetType == 4) {
					if (actionType == 1) {
						let addActionSupportList = this.addActionSupportList;
						let targetIndex = addActionSupportList.findIndex(item => item.targetId == targetId);
						addActionSupportList[targetIndex].status = status;
						addActionSupportList[targetIndex].count = addActionSupportList[targetIndex].count + (status ==
							"1" ? 1 : -1);
						this.addActionSupportList = addActionSupportList;
					}
					if (actionType == 2) {
						let addActionAgainstList = this.addActionAgainstList;
						let targetIndex = addActionAgainstList.findIndex(item => item.targetId == targetId);
						addActionAgainstList[targetIndex].status = status;
						addActionAgainstList[targetIndex].count = addActionAgainstList[targetIndex].count + (status ==
							"1" ? 1 : -1);
						this.addActionAgainstList = addActionAgainstList;
					}
				}
			},

			action(e) {
				let data = {
					status: e[0],
					actionType: e[1],
					targetType: e[2],
					targetId: e[3]
				};
				doAction(data).then(res => {
					if (res.code == 200) {
						if (data.targetType == 1) {
							let paperActionTarget = this.paperActionTarget;
							paperActionTarget.status = data.status;
							this.paperActionTarget = paperActionTarget;
						} else if(data.targetType == 3 || data.targetType == 4) {
							if (data.status == '1') {
								let data1 = JSON.parse(JSON.stringify(data));
								data1.status = '0';
								let doAc = false;
								if (data1.actionType == 1) {
									// 移除踩
									data1.actionType = 2;
									// 参考答案
									if(data.targetType == 3) {
										let target = this.answerActionAgainstList.find(item => item.targetId === data.targetId);
										if(target && target.status == '1') {
											doAc = true;
										}
									}
									// 补充答案
									if(data.targetType == 4) {
										let target = this.addActionAgainstList.find(item => item.targetId === data.targetId);
										if(target && target.status == '1') {
											doAc = true;
										}
									}
								} else if (data1.actionType == 2) {
									// 移除赞
									data1.actionType = 1;
									// 参考答案
									if(data.targetType == 3) {
										let target = this.answerActionSupportList.find(item => item.targetId === data.targetId);
										if(target && target.status == '1') {
											doAc = true;
										}
									}
									// 补充答案
								    if(data.targetType == 4) {
										let target = this.addActionSupportList.find(item => item.targetId === data.targetId);
										if(target && target.status == '1') {
											doAc = true;
										}
								    }
								}
								if (doAc) {
									doAction(data1).then(res => {
										if (res.code == 200) {
											this.changeActionStatus(data.targetType, data1.actionType, data1.targetId, '0');
										}
									});
								}
							}
							this.changeActionStatus(data.targetType, data.actionType, data.targetId, data.status);
						}
					}
				})
			},

			handleUserAddAnswerShow() {
				let filter = {
					questionId: this.questionId,
					pageSize: this.pageSize,
					pageNum: this.pageNum,
					status: "1",
				};
				getUserAddAnswerList(filter).then(res => {
					let userAddAnswers = [];
					for (let key1 in res.rows) {
						let target = res.rows[key1];
						let content = target.content.toString();
						let result = highLight(content, '', 5);
						for (let key2 in result) {
							content = content.replace(new RegExp(result[key2], "gm"),
								`<span style="color:#FF9C07;">${result[key2]}</span>`)
						}
						target.content = content;
						userAddAnswers.push(target);
					}
					this.userAddAnswerList = userAddAnswers;
					let params = {};
					params.targetIds = '';
					for (let key in userAddAnswers) {
						params.targetIds = params.targetIds + userAddAnswers[key].addId + ",";
					}
					params.targetIds = params.targetIds.slice(0, -1);
					params.targetType = 4;
					// 补充答案点赞数量
					params.actionType = 1;
					getTargetsActionStatus(params).then(res => {
						this.addActionSupportList = res.data;
					});
					// 补充答案点踩数量
					params.actionType = 2;
					getTargetsActionStatus(params).then(res => {
						this.addActionAgainstList = res.data;
					});
				}).catch(err => {
					console.log(err)
				});
			},

			handleCopy(content){
				// 处理特殊HTML实体
				let cleanContent = content
					.replace(/<br\s*\/?>/gi, '\n')  // 将<br>转换为换行
					.replace(/&nbsp;/g, ' ')        // 处理空格
					.replace(/&amp;/g, '&')         // 处理&符号
					.replace(/&lt;/g, '<')          // 处理<符号
					.replace(/&gt;/g, '>')          // 处理>符号
					.replace(/<div.*?>/gi, '')      // 移除div开始标签
					.replace(/<\/div>/gi, '\n')     // div结束标签转换为换行
					.replace(/<p.*?>/gi, '')        // 移除p开始标签
					.replace(/<\/p>/gi, '\n')       // p结束标签转换为换行
					.replace(/<[^>]+>/g, '')        // 移除其他所有HTML标签
					.replace(/\n{3,}/g, '\n\n')     // 将多个连续换行减少为最多两个
					.trim();                        // 移除首尾空白
				uni.setClipboardData({
					data: cleanContent,
					success: function(res) {
						uni.showToast({
							title: '复制成功',
							icon: 'success',
							duration: 1000
						});
					}
				});
			},

			handleAnswerFullScreen(type) {
				this.fullScreenExpand = !this.fullScreenExpand;
				this.defineBarShow = !this.fullScreenExpand;
			},

			selectPaperList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getPaperList(filter).then(res => {
					if (res.rows.length > 0) {
						this.paperTitle = res.rows[0].title;
						uni.setNavigationBarTitle({
						  title: this.paperTitle
						})
						// 试卷收藏
						let params = {};
						params.targetType = 1;
						params.actionType = 3
						params.targetIds = res.rows[0].paperId;
						getTargetsActionStatus(params).then(res => {
							let paperActionTarget = res.data.find(item => item.targetId == this.paperId);
							this.paperActionTarget = paperActionTarget;
						});
					}
				});
			},

			selectQuestionList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getQuestionList(filter).then(res => {
					this.questionList = res.rows;
					this.handleQuestionShow();
					for (let key in this.questionList) {
						let item = this.questionList[key];
						if (item.questionIndex == this.questionIndex) {
							this.questionId = item.questionId;
							let filter = {
								questionId: item.questionId
							};
							this.selectAnswerList(filter);
						}
					}
				});
			},
			
			selectMaterialList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getMaterialList(filter).then(res => {
					this.materialList = res.rows;
					this.loadingFlag = false;
					this.handleMaterialShow();
				}).catch(err => {
					console.log(err)
				});
			},
			
			selectMaterial(index) {
				this.materialIndex = index;
				this.handleMaterialShow();
			},
			
			handleMaterialShow() {
				let material = null;
				this.materialList.forEach(item => {
					if (item.materialIndex == this.materialIndex) {
						material = item;
					}
				});
				this.materialContent = material.content;
			},

			selectAnswerList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getAnswerList(filter).then(res => {
					this.answerList = res.rows;
					this.loadingFlag = false;
					this.handleAnswerShow();
					this.handleUserAddAnswerShow();
				});
			},
			// 选择答案
			selectAnswer(item, type, index) {
				this.selectedAnswerType = type;
				this.selectedAnswerIndex = index;
				this.selectedAnswerContent = item.content;
				
				// 直接触发确认
				// 发送事件，将答案内容传回字帖页面
				uni.$emit('ztAnswerSelected', this.selectedAnswerContent);
				
				// 显示提示
				uni.showToast({
					title: '已选择答案',
					icon: 'success',
					duration: 1500
				});
				
				// 延迟返回
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			},
		},

		onBackPress(options) {
			if (this.fullScreenExpand) {
				this.fullScreenExpand = false;
				this.defineBarShow = true;
				return true;
			}
		},
		
		
		onShow() {
			this.currentFontSize = uni.getStorageSync("fontSize") || 14;
			this.metaKey += 1;
		},
		
		onResize() {
			this.metaKey += 1;
		},

		onLoad(options) {
			var that = this;
			uni.getSystemInfo({
				success: function (res) {
					that.screenHeight = res.windowHeight * (750 / res.windowWidth),
					that.pix = 750 / res.windowWidth
				}
			});
			this.animation = uni.createAnimation({
				duration: 1500,
				timingFunction: 'ease',
			});
			let paperId = options.paperId;
			let questionIndex = options.questionIndex ? options.questionIndex : 1;
			let ocrParamStr = options.ocrParam;
			let scanContent = '';
			if (ocrParamStr) {
				let ocrParam = JSON.parse(decodeURIComponent(ocrParamStr));
				scanContent = ocrParam.scanContent;
				paperId = ocrParam.paperId;
				questionIndex = ocrParam.questionIndex;
				this.defineBarShow = false;
				setTimeout(() => {
					richText.editorCtx.insertText({
						text: scanContent
					});
				}, 500);
			}

			this.paperId = paperId;
			this.questionIndex = questionIndex;
			this.activeTab = options.activeTab ? options.activeTab : 'paper';
			this.answerTab = options.answerTab ? options.answerTab : 'answer';
			this.userAnswerContentLenght = scanContent ? scanContent.replace(' ', '').length : 0;
			this.adStatus = adStatus();
			let filter = {
				paperId: paperId,
			}
			this.selectPaperList(filter);
			this.selectQuestionList(filter);
			this.selectMaterialList(filter);
			this.fromZt = options.fromZt === 'true';
		},
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.full-answer-context {
		position: absolute;
		z-index: 1000;
		padding: 0 24rpx;
		left: 0rpx;
		.answer-tool {
			display: flex;
			flex-direction: row;
			height: 6vh;
			justify-content: flex-end;
			align-items: center;
			border-bottom: 1px solid #FAFAFA;
			padding: 0 15rpx;
			image {
				width: 40rpx;
				height: 40rpx;
				margin-left: 20rpx;
			}
		}
		.content-wrapper {
			font-size: 28rpx;
			letter-spacing: 1rpx;
			height: 92vh;
			padding: 12rpx 24rpx;
			overflow-y: scroll;
			border-radius: 20rpx;
			background: rgba(70, 180, 177, 0.1);
		}
	}
	
	.not-full {
		display: flex;
		flex-direction: column;
		width: 100%;
		padding-bottom: 150rpx;
	}
	
	.questions {
		width: 100%;	
		margin-bottom: 16rpx;	
	}

	.functions {
		display: flex;
		justify-content: center;
		margin-bottom: 16rpx;
		position: relative;
		.function-buttons {
			position: relative;
			border: 1px solid #EBEBEB;
			border-radius: 12rpx;
			display: flex;
			justify-content: center;
			gap: 20rpx;
			padding: 8rpx;
			.button-item {
				padding: 4rpx 14rpx;
				border-radius: 10rpx;
				color: #666;
				font-size: 28rpx;
				transition: all 0.3s ease;
				&.active {
					background-color: #46B4B1;
					color: #FFF;
					font-weight: bold;
				}
		
				&:active {
					transform: scale(0.95);
				}
			}
			font-size: $uni-font-size-base;
			height: auto;
			display: flex;
			justify-content: center;
			align-items: center;
			color: $uni-color-primary;
		}
		.tool {
			display: flex;
			align-items: center;
			position: absolute;
			right: 0rpx;
			image {
				padding: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	
	.section-list {
		display: flex;
		justify-content: space-between;
		align-items: center;
		overflow-x: scroll;
		width: 100%;
		padding-bottom: 16rpx;	
		.question-section, .material-section {
			.title {
				display: flex;
				padding: 10rpx 20rpx;
				height: 35rpx;
				font-size: $uni-font-size-base;
				font-style: normal;
				flex-direction: row;
				justify-content: center;
				width: 100rpx;
			}
			.show_bar {
				display: flex;
				height: 4rpx;
				border-radius: 20rpx;
				background: $uni-color-primary;
				width: 40%;
				margin: 0 30%;
			}
			.hide_bar {
				display: none;
				display: flex;
				height: 4rpx;
				border-radius: 20rpx;
				background: $uni-color-primary;
				width: 40%;
				margin: 0 30%;
			}
		}
	}

	.question-context, .material-context {
		display: flex;
		flex-direction: column;
		padding: 12rpx 24rpx;
		overflow-y: scroll;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
		font-size: $uni-font-size-base;
	}
	
	.answers-area {
		background: rgba(70, 180, 177, 0.1);
		border-radius: 20rpx;
		font-size: $uni-font-size-base;
		letter-spacing: 1rpx;
		.answer-types-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 16rpx 0rpx;
			padding: 12rpx 24rpx;
			position: relative;
			.answer-types {
				background-color: #fff;
				border-radius: 12rpx;
				display: flex;
				justify-content: center;
				padding: 8rpx 16rpx;
				.button-item {
					padding: 4rpx 14rpx;
					border-radius: 10rpx;
					color: #666;
					font-size: 28rpx;
					transition: all 0.3s ease;
					&.active {
						background-color: #46B4B1;
						color: #FFF;
						font-weight: bold;
					}
			
					&:active {
						transform: scale(0.95);
					}
				}
				font-size: $uni-font-size-base;
				height: auto;
				display: flex;
				justify-content: center;
				align-items: center;
				color: $uni-color-primary;
			}
			.answer-tool{
				position: absolute;
				right: 0rpx;
				image {
					padding: 16rpx;
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
		.answer-context {
			padding: 12rpx 24rpx;
		}
	}
	
	.user-add-answers .answer-title{
		margin: 16rpx 0rpx;
		display: flex;
		align-items: center;
		.left-icon {
			display: inline-block;
			position: relative;
			width: 16rpx;
			height: 56rpx;
			border-radius: 20rpx;
			background: $uni-color-primary;
		}
		.title {
			position: relative;
			left: 20rpx;
			font-size: $uni-font-size-base;
		}
	}
	
	.no-permission {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		// margin-top: 120rpx;
		.desc {
			color: $uni-text-color-placeholder;
			font-size: $uni-font-size-base;
			margin-top: 40rpx;
		}
		image {
			width: 442rpx;
			height: 274rpx;
		}
	}

	.user-add-answers .add {
		display: flex;
		align-items: center;
		position: relative;
		color: red;
		font-size: $uni-font-size-base;
		text-decoration: underline;
	}
	
	.full-answer-context .content-wrapper,
	.answers .content ,
	.user-add-answers .content  {
		::v-deep img {
			display: none;
		}
	} 

	.full-answer-context .content-wrapper .header,
	.answers .content .header,
	.user-add-answers .content .header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 56rpx;
	}

	.full-answer-context .content-wrapper .header .left,
	.answers .content .header .left,
	.user-add-answers .content .header .left {
		color: $uni-color-primary;
		display: flex;
		align-items: center;
	}
	
	.full-answer-context .content-wrapper .header .left .copy,
	.answers .content .header .left .copy {
		padding: 12rpx;
		image {
			width: 30rpx;
			height: 30rpx;
			
		}
	}

	.full-answer-context .content-wrapper .header .right,
	.answers .content .header .right,
	.user-add-answers .content .header .right {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
	
	.full-answer-context .content-wrapper .header .right {
		margin-left: auto;
	}

	.full-answer-context .content-wrapper .header .right image,
	.answers .content .header .right image,
	.user-add-answers .content .header .right image {
		width: 40rpx;
		height: 40rpx;
		padding: 0 14rpx;
		margin-left: 14rpx;
	}

	.user-answer {
		.answer-tool {
			margin-bottom: 16rpx;
			display: flex;
			justify-content: center;
			.commit-button {
				display: flex;
				justify-content: center;
				height: 64rpx;
				border-radius: 40rpx;
				background-color: $uni-color-primary;
				color: $uni-bg-color;
				font-size: $uni-font-size-base;
				padding: 0 36rpx;
				align-items: center;
			}
		}
		.content {
			padding: 24rpx;
			padding-top: 0rpx;
			margin-bottom: 16rpx;
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			letter-spacing: 1rpx;
			border-radius: 20rpx;
			background: #FFF;
			overflow-y: scroll;
			.length {
				display: flex;
				color: $uni-color-primary;
				justify-content: flex-end;
				font-size: $uni-font-size-sm;
			}
		}
	}

	.define-bar {
		display: flex;
		justify-content: space-around;
		background-color: $uni-bg-color;
		align-items: center;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 16rpx 0rpx 24rpx 0rpx;
		display: flex;
		z-index: 100;
		border-top: 1rpx solid $uni-text-color-disable;
		.menu-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			text {
				font-size: 24rpx;
				color: $uni-color-primary;
				margin-top: 14rpx;
			}
			image {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}

	.popup .maskLayer {
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		background: #000;
		opacity: 0.2;
		overflow: hidden;
		z-index: 100;
		color: #fff;
	}

	.popup .choose {
		overflow: hidden;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 200;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-content: center;
		justify-content: center;
		transform: translateY(100vh);
	}

	.popup .choose .move-tag {
		width: 100%;
		z-index: 200;
		height: 60rpx;
		display: flex;
		justify-content: center;
		position: relative;
		margin-bottom: -20rpx;
	}

	.popup .choose .move-tag image {
		height: 100%;
		width: 200rpx;
	}

	.popup .choose .list {
		background: $uni-bg-color;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		display: flex;
		align-content: center;
		justify-content: center;
		flex-direction: column;
		overflow-y: scroll;
		position: relative;
		padding: 28rpx;
		width: calc(100% - 56rpx);
	}

	.popup .choose .input-area-wrapper {
		background: $uni-bg-color;
		padding: 0 28rpx;
	}

	.popup .choose .input-area {
		position: relative;
		border-radius: 10rpx;
		background: #FAFAFA;
		margin-bottom: 20rpx;
		display: flex;
	}

	.popup .choose .input-area textarea {
		padding: 24rpx;
		width: 85%;
		height: 120rpx;
		font-size: 28rpx;
	}


	.popup .choose .input-area .commit-button {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 15%;
		color: $uni-color-primary;
		font-size: 28rpx;
	}

	.list .context {
		position: relative;
		word-wrap: break-word;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.list .context .content {
		padding: 24rpx;
		padding-right: 48rpx;
		font-size: 28rpx;
		letter-spacing: 1rpx;
		height: auto;
	}

	.list .context .header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding: 12rpx 24rpx 0rpx 24rpx;
	}

	.list .context .header .avatar image {
		border-radius: 50%;
		background-color: #D9D9D9;
		width: 50rpx;
		height: 50rpx;
		margin-right: 20rpx;
	}

	.list .context .header .nickname {
		font-size: 24rpx;
		margin-right: 40rpx;
	}

	.list .context .header .delete-button {
		display: flex;
		justify-content: center;
		text-align: center;
		color: #4C73D9;
		font-size: 20rpx;
		border: 1px solid #4C73D9;
		border-radius: 10rpx;
		margin-right: 40rpx;
		padding: 5rpx;
		padding-bottom: 8rpx;
	}

	.list .context .header .create-time {
		color: #838383;
		font-size: 20rpx;
		align-self: flex-end;
		margin-left: auto;
	}

	.list .context .header .support {
		margin-left: 20rpx;
		color: #838383;
		font-size: 20rpx;
	}

	.list .context .header view {
		display: flex;
	}

	.list .context .delete image {
		width: 44rpx;
		height: 44rpx;
		position: absolute;
		right: 0rpx;
		top: 0rpx;
	}

	.list .context .footer {
		display: flex;
		justify-content: space-between;
	}

	.list .context .footer .content-length {
		position: relative;
		display: inline-block;
		margin-bottom: 20rpx;
		color: $uni-color-primary;
		;
		text-align: center;
		font-size: 24rpx;
		margin-left: 20rpx;
	}

	.list .context .footer .date-time {
		position: relative;
		display: inline-block;
		margin-bottom: 20rpx;
		color: $uni-color-primary;
		;
		text-align: center;
		font-size: 24rpx;
		margin-right: 20rpx;
	}

	.list .no-more {
		margin-top: 40rpx;
		color: $uni-color-primary;
		;
		text-align: center;
		font-size: 28rpx;
	}
	/* 添加选择器样式 */
	.zt-answer-selector {
		background-color: #fff;
		padding: 20rpx;
		margin: 20rpx;
		border-radius: 10rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.selector-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.selector-button {
		background-color: #46B4B1;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
	}

	/* 单选按钮样式 - 确保样式更明显 */
	.radio-select {
		display: inline-flex;
		align-items: center;
		margin-left: 20rpx;
		padding: 10rpx; /* 增加点击区域 */
	}

	.radio-button {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		border: 2rpx solid $uni-color-warning;
		position: relative;
		box-sizing: border-box; /* 确保边框计入宽高 */
		background-color: #fff; /* 添加背景色 */
	}

	.radio-label {
		margin-left: 10rpx;
		color: $uni-color-warning;
	}

	.radio-button.selected:after {
		content: '';
		position: absolute;
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: $uni-color-warning;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	/* 调整header样式以适应单选按钮 */
	.header .left {
		display: flex;
		align-items: center;
	}
</style>
