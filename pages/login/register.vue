<template>
	<fui-toast ref="toast"></fui-toast>
	<view class="page-body">
		<form @submit="onSubmit">
			<view class="title">欢迎使用囊中对比</view>
			<view class="input-group" :class="accountFocus ? 'input-focus' : ''">
				<input class="input" type="text" placeholder="邮箱"
					@focus="handleFocus('account')" @input="bindTextInput($event,'account')">
			</view>
			<text class="error" v-show="errors.account">{{ errors.account }}</text>
			<view class="input-group" :class="codeFocus ? 'input-focus' : ''">
				<input class="input" placeholder="验证码"
					@focus="handleFocus('code')" @input="bindTextInput($event,'code')">
				<view class="code" @click="handleClickSend">{{ buttonText}}</view>
			</view>
			<text class="error" v-show="errors.code">{{ errors.code }}</text>
			<view class="input-group" :class="passwordFocus ? 'input-focus' : ''">
				<input class="input" :type="eye1 ? 'text': 'password'" placeholder="密码"
				 @focus="handleFocus('password')" @input="bindTextInput($event,'password')">
				<image class="icon" :src="iconSrc1" @click="eye1 = !eye1" />
			</view>
			<text class="error" v-show="errors.password">{{ errors.password }}</text>
			<view class="input-group" :class="confirmPasswordFocus ? 'input-focus' : ''">
				<input class="input" :type="eye2 ? 'text': 'password'" placeholder="确认密码"
				 @focus="handleFocus('confirmPassword')"  @input="bindTextInput($event,'confirmPassword')">
				<image class="icon" :src="iconSrc2" @click="eye2 = !eye2" />
			</view>
			<text class="error" v-show="errors.confirmPassword">{{ errors.confirmPassword }}</text>
			<button class="submit-button" size="default" form-type="submit">注册</button>
		</form>
		<view class="agreement">
			<radio :checked="agreement" color="#46B4B1" style="transform:scale(0.7);" @click="agreement=!agreement" />
			<text class="normal">我已阅读并同意</text>
			<text class="link" @click="handleAgreementDetail(1)">服务协议</text>
			<text class="normal">和</text>
			<text class="link" @click="handleAgreementDetail(2)">隐私政策</text>
			<text class="normal">我已阅读并同意</text>
		</view>
		<DefineDialog class="submit-dialog" v-if="submitDialog" @cancel="cancelSubmit" @confirm="confirmSubmit">
			<view class="title">服务协议及隐私保护</view>
			<view class="desc">
				<p class="normal">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;欢迎使用囊中对比，我们非常重视您的个人信息和隐私保护，在您使用囊中对比之前，请仔细阅读
				<text class="link" @click="handleAgreementDetail(1)">服务协议</text>
				和
				<text class="link" @click="handleAgreementDetail(2)">隐私政策</text>
				我们将按照您同意的条款使用您的个人信息，以便给您提供服务。
				</p>
			</view>
		</DefineDialog>
	</view>
</template>

<script>
	import {
		login,
		sendEmailCode,
		register
	} from '@/api/index';
	export default {
		data() {
			return {
				account: '',
				code: '',
				password: '',
				uuid: '',
				confirmPassword: '',
				agreement: false,
				submitDialog: false,
				passwordFocus: false,
				confirmPasswordFocus: false,
				accountFocus: false,
				codeFocus: false,
				countdownActive: false,
				countdown: 60,
				buttonText: '获取验证码',
				errors: {
					account: '',
					code: '',
					password: '',
					confirmPassword: '',
				},
				eye1: false,
				eye2: false,
			}
		},
		computed: {
			iconSrc1() {
				return this.eye1 ? '/static/images/preview-open.png' : '/static/images/preview-close.png';
			},
			iconSrc2() {
				return this.eye2 ? '/static/images/preview-open.png' : '/static/images/preview-close.png';
			},
		},
		methods: {
			onSubmit(e) {
				if (!this.agreement) {
					this.submitDialog = true;
					return;
				}
				if (this.validateForm()) {
					this.submitForm();
				}
			},

			handleFocus(type) {
				this.accountFocus = false;
				this.codeFocus = false;
				this.passwordFocus = false;
				this.confirmPasswordFocus = false;
				if (type == 'account') {
					this.accountFocus = true;
					this.errors.account = '';
				} else if (type == 'code') {
					this.codeFocus = true;
					this.errors.code = '';
				} else if (type == 'password') {
					this.passwordFocus = true;
					this.errors.password = '';
				} else if (type == 'confirmPassword') {
					this.confirmPasswordFocus = true;
					this.errors.confirmPassword = '';
				}
			},

			handleBlur(type) {
				const account = this.account.trim();
				const code = this.code.trim();
				const password = this.password.trim();
				const confirmPassword = this.confirmPassword.trim();
				if (type == 'account') {
					this.accountFocus = false;
					const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
					if (!emailRegex.test(account)) {
						this.errors.account = '请输入正确的邮箱地址';
					}
				} else if (type == 'code') {
					this.codeFocus = false;

					// 验证验证码长度
					if (code.length !== 6) {
						this.errors.code = '验证码长度应为6位';
					}
				} else if (type == 'password') {
					this.passwordFocus = false;

					// 密码复杂度验证（这里只是一个简单的示例）
					if (!password || password.length < 6) {
						this.errors.password = '密码长度至少为6位';
					}
				} else if (type == 'confirmPassword') {
					this.confirmPasswordFocus = false;
					// 确认密码与密码是否一致
					if (password !== confirmPassword) {
						this.errors.confirmPassword = '确认密码与密码不符';
					}
				}
			},
			
			bindTextInput(e, type) {
				if(type === 'account') {
					this.account = e.detail.value;
				} else if(type === 'code') {
					this.code = e.detail.value;
				} else if(type === 'password') {
					this.password = e.detail.value;
				} else if(type === 'confirmPassword') {
					this.confirmPassword = e.detail.value;
				}
			},

			validateForm() {
				this.errors = {}; // 清除错误信息
				const account = this.account.trim();
				const code = this.code.trim();
				const password = this.password.trim();
				const confirmPassword = this.confirmPassword.trim();
				// 验证邮箱格式
				const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
				if (!emailRegex.test(account)) {
					this.errors.account = '请输入正确的邮箱地址';
				}
				// 验证验证码长度
				if (code.length !== 6) {
					this.errors.code = '验证码长度应为6位';
				}
				// 密码复杂度验证（这里只是一个简单的示例）
				if (!password || password.length < 6) {
					this.errors.password = '密码长度至少为6位';
				}
				// 确认密码与密码是否一致
				if (password !== confirmPassword) {
					this.errors.confirmPassword = '确认密码与密码不符';
				}
				return Object.keys(this.errors).length === 0;
			},

			startCountdown() {
				this.countdownActive = true;
				this.countdown = 60; // 重置倒计时为60秒
				this.buttonText = `${this.countdown}s 后重试`;
				const intervalId = setInterval(() => {
					this.countdown--;
					this.buttonText = `${this.countdown}s 后重试`;
					// 当倒计时结束时，清除间隔并重置状态
					if (this.countdown === 0) {
						clearInterval(intervalId);
						this.countdownActive = false;
						this.buttonText = '重新发送';
					}
				}, 1000);
			},

			handleClickSend() {
				this.handleBlur('account');
				if (this.errors.account) {
					return;
				}
				if (!this.countdownActive) {
					let filter = {
						email: this.account.trim(),
					};
					// 开始倒计时
					this.startCountdown();
					let options = {
						text: '验证码已发送，请查收邮箱',
					};
					this.$refs.toast.show(options);
					sendEmailCode(filter).then(res => {
							if (res.code == 200) {
								this.uuid = res.uuid;
							}
						})
						.catch(err => {
							console.log(err);
						});
				}
			},
			
			handleAgreementDetail(type) {
				if (type == 1) {
					uni.navigateTo({
						url: "/pages/service/agreement"
					})
				}
				if (type == 2) {
					uni.navigateTo({
						url: "/pages/service/privicy"
					})
				}
			},
			
			cancelSubmit() {
				this.submitDialog = false;
			},
			
			confirmSubmit() {
				this.agreement = true;
				this.submitDialog = false;
				this.onSubmit();
			},
			
			submitForm() {
				let filter = {
					account: this.account.trim(),
					code: this.code.trim(),
					password: this.password,
					uuid: this.uuid
				}
				console.log(filter);
				register(filter).then(res => {
					let options = {
						text: res.msg
					}
					this.$refs.toast.show(options);
					if (res.code == 200) {
						setTimeout(() => {
							uni.reLaunch({
								url: "/pages/login/index?account=" + this.account
							})
						}, 2000);
					}
				}).catch(err => {
					console.log(err)
				});
			}
		}
	}
</script>


<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.title {
		margin-top: 60rpx;
		font-size: $uni-font-size-lm;
	}


	.input-group {
		display: flex;
		align-items: center;
		margin-top: 60rpx;
		width: 684rpx;
		background-color: $uni-bg-color-grey;
		padding: 0 20rpx;
		height: 90rpx;
		box-sizing: border-box;
		border-radius: 14rpx;

		& .icon {
			margin-right: 10px;
			width: 36rpx;
			height: 36rpx;
		}

		& .input {
			flex: 1;
			border: none;
			outline: none;
			background-color: transparent;
			color: inherit;
			padding: 0;
			margin: 0;
			width: 100%;
			height: 100%;
			box-sizing: border-box;

			&::placeholder {
				color: $uni-text-color-placeholder;
			}
		}

		& .code {
			font-size: $uni-font-size-sm;
			color: $uni-color-primary;
		}
	}

	.input-focus {
		border: 1px solid $uni-color-primary;
	}

	.error {
		color: $uni-color-error;
		font-size: $uni-font-size-sm;
		padding: 0 36rpx;
		margin-top: 8rpx;
	}


	.submit-button {
		margin-top: 60rpx;
		width: 684rpx;
		background-color: $uni-color-primary;
		color: #fff;
		font-size: $uni-font-size-lg;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
	.agreement {
		display: flex;
		flex-direction: row;
		margin-top: auto;
		align-items: center;
		font-size: $uni-font-size-sm;
		margin-bottom: 20rpx;
	}
	
	.agreement .normal {
		color: $uni-text-color-disable;
	}
	
	.agreement .link {
		text-decoration-line: underline;
		color: $uni-text-color;
		padding: 0 8rpx;
	}
	
	.submit-dialog .title {
		width: 100%;
		text-align: center;
		font-size: $uni-font-size-lm;
		margin-bottom: 32rpx;
	}
	
	.submit-dialog  .desc {
		color: $uni-text-color-disable;
		font-size: $uni-font-size-sm;
		padding: 0 28rpx;
	}
	
	.submit-dialog .desc .normal {
		color: $uni-text-color-disable;
	}
	
	.submit-dialog .desc .link {
		text-decoration-line: underline;
		color: $uni-color-primary;
		padding: 0 8rpx;
	}
</style>