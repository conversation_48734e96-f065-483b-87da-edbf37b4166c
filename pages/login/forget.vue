<template>
	<fui-toast ref="toast"></fui-toast>
	<view class="page-body">
		<form @submit="onSubmit">
			<view class="title">欢迎使用囊中对比</view>
			<view class="input-group" :class="accountFocus ? 'input-focus' : ''">
				<input class="input" type="text" @focus="handleFocus('account')"
				 @input="bindTextInput($event,'account')" placeholder="邮箱">
			</view>
			<text class="error" v-show="errors.account">{{ errors.account }}</text>

			<view class="input-group" :class="codeFocus ? 'input-focus' : ''">
				<input class="input" placeholder="验证码"
					@focus="handleFocus('code')" @input="bindTextInput($event,'code')">
				<view class="code" @click="handleClickSend">{{ buttonText}}</view>
			</view>
			<text class="error" v-show="errors.code">{{ errors.code }}</text>

			<view class="input-group" :class="passwordFocus ? 'input-focus' : ''">
				<input class="input" :type="eye ? 'text': 'password'"
					@focus="handleFocus('password')" @input="bindTextInput($event,'password')" placeholder="请输入新密码">
				<image class="icon-eye" :src="iconSrc" @click="eye = !eye" />
			</view>

			<text class="error" v-show="errors.password">{{ errors.password }}</text>
			<button class="submit-button" size="default" form-type="submit">重置密码</button>
		</form>
	</view>
</template>

<script>
	import {
		login,
		sendEmailCode,
		resetPassword
	} from '@/api/index';
	export default {
		data() {
			return {
				account: '',
				code: '',
				password: '',
				uuid: '',
				accountFocus: false,
				codeFocus: false,
				passwordFocus: false,
				countdownActive: false,
				countdown: 60,
				buttonText: '获取验证码',
				errors: {
					account: '',
					code: '',
					password: '',
				},
				eye: false
			}
		},
		computed: {
			iconSrc() {
				return this.eye ? '/static/images/preview-open.png' : '/static/images/preview-close.png';
			},
		},
		methods: {

			onSubmit(e) {
				if (this.validateForm()) {
					this.submitForm();
				}
			},

			handleFocus(type) {
				this.accountFocus = false;
				this.codeFocus = false;
				this.passwordFocus = false;
				if (type == 'account') {
					this.accountFocus = true;
					this.errors.account = '';
				} else if (type == 'code') {
					this.codeFocus = true;
					this.errors.code = '';
				} else if (type == 'password') {
					this.passwordFocus = true;
					this.errors.password = '';
				}
			},

			handleBlur(type) {
				const account = this.account.trim();
				const code = this.code.trim();
				const password = this.password.trim();
				if (type == 'account') {
					this.accountFocus = false;
					const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
					if (!emailRegex.test(account)) {
						this.errors.account = '请输入正确的邮箱地址';
					}
				} else if (type == 'code') {
					this.codeFocus = false;

					// 验证验证码长度
					if (code.length !== 6) {
						this.errors.code = '验证码长度应为6位';
					}
				} else if (type == 'password') {
					this.passwordFocus = false;

					// 密码复杂度验证（这里只是一个简单的示例）
					if (!password || password.length < 6) {
						this.errors.password = '密码长度至少为6位';
					}
				}
			},
			
			bindTextInput(e, type) {
				if(type === 'account') {
					this.account = e.detail.value;
				} else if(type === 'code') {
					this.code = e.detail.value;
				} else if(type === 'password') {
					this.password = e.detail.value;
				}
			},

			validateForm() {
				this.errors = {}; // 清除错误信息
				const account = this.account.trim();
				const code = this.code.trim();
				const password = this.password.trim();
				// 验证邮箱格式
				const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
				if (!emailRegex.test(account)) {
					this.errors.account = '请输入正确的邮箱地址';
				}
				// 验证验证码长度
				if (code.length !== 6) {
					this.errors.code = '验证码长度应为6位';
				}
				// 密码复杂度验证（这里只是一个简单的示例）
				if (!password || password.length < 6) {
					this.errors.password = '密码长度至少为6位';
				}
				return Object.keys(this.errors).length === 0;
			},

			startCountdown() {
				this.countdownActive = true;
				this.countdown = 60; // 重置倒计时为60秒
				this.buttonText = `${this.countdown}s 后重试`;
				const intervalId = setInterval(() => {
					this.countdown--;
					this.buttonText = `${this.countdown}s 后重试`;
					// 当倒计时结束时，清除间隔并重置状态
					if (this.countdown === 0) {
						clearInterval(intervalId);
						this.countdownActive = false;
						this.buttonText = '重新发送';
					}
				}, 1000);
			},

			handleClickSend() {
				this.handleBlur('account');
				if (this.errors.account) {
					return;
				}
				if (!this.countdownActive) {
					let filter = {
						email: this.account.trim(),
					};
					// 开始倒计时
					this.startCountdown();
					let options = {
						text: '验证码已发送，请查收邮箱',
					};
					this.$refs.toast.show(options);
					sendEmailCode(filter).then(res => {
							if (res.code == 200) {
								this.uuid = res.uuid;
							}
						})
						.catch(err => {
							console.log(err);
						});
				}
			},

			submitForm() {
				let filter = {
					account: this.account.trim(),
					code: this.code.trim(),
					password: this.password,
					uuid: this.uuid
				}
				console.log(filter);
				resetPassword(filter).then(res => {
					console.log(res);
					let options = {
						text: res.msg
					}
					this.$refs.toast.show(options);
					if (res.code == 200) {
						setTimeout(() => {
							uni.reLaunch({
								url: "/pages/login/index?account=" + this.account
							})
						}, 2000);
					}
				}).catch(err => {
					console.log(err)
				});
			}
		}
	}
</script>


<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.title {
		margin-top: 60rpx;
		font-size: $uni-font-size-lm;
	}


	.input-group {
		display: flex;
		align-items: center;
		margin-top: 60rpx;
		width: 684rpx;
		background-color: $uni-bg-color-grey;
		padding: 0 20rpx;
		height: 90rpx;
		box-sizing: border-box;
		border-radius: 14rpx;

		& .icon-eye {
			margin-right: 10px;
			width: 36rpx;
			height: 36rpx;
		}

		& .input {
			flex: 1;
			border: none;
			outline: none;
			background-color: transparent;
			color: inherit;
			padding: 0;
			margin: 0;
			width: 100%;
			height: 100%;
			box-sizing: border-box;

			&::placeholder {
				color: $uni-text-color-placeholder;
			}
		}

		& .code {
			font-size: $uni-font-size-sm;
			color: $uni-color-primary;
		}
	}

	.input-focus {
		border: 1px solid $uni-color-primary;
	}

	.error {
		color: $uni-color-error;
		font-size: $uni-font-size-sm;
		padding: 0 36rpx;
		margin-top: 8rpx;
	}


	.submit-button {
		margin-top: 60rpx;
		width: 684rpx;
		background-color: $uni-color-primary;
		color: #fff;
		font-size: $uni-font-size-lg;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
</style>