<template>
	<fui-toast ref="toast"></fui-toast>
	<view class="page-body">
		<form @submit="onSubmit">
			<view class="title">欢迎使用囊中对比</view>

			<view class="input-group" :class="accountFocus ? 'input-focus' : ''">
				<image class="icon" src="@/static/images/mail.png" />
				<input class="input" type="text" @focus="handleFocus('account')"
				 @input="bindTextInput($event,'account')" placeholder="邮箱" ref="accountInput">
			</view>
			<text class="error" v-show="errors.account">{{ errors.account }}</text>

			<view class="input-group" :class="passwordFocus ? 'input-focus' : ''">
				<image class="icon" src="@/static/images/lock.png" />
				<input class="input" :type="eye ? 'text': 'password'"
					@focus="handleFocus('password')" @input="bindTextInput($event,'password')" placeholder="密码"
					ref="passwordInput">
				<image class="icon" :src="iconSrc" @click="eye = !eye" />
			</view>
			<text class="error" v-show="errors.password">{{ errors.password }}</text>

			<button class="submit-button" size="default" form-type="submit">登录</button>
		</form>
		<view class="other-oper">
			<view class="register" @click="handleOtherOper(1)">注册新账号</view>
			<view class="forget" @click="handleOtherOper(2)">忘记密码？</view>
		</view>
		<view class="third-login">
			<view class="title">
				<view class="border"></view>
				第三方登录
				<view class="border"></view>
			</view>
			<view class="login-type">
				<image src="@/static/images/wxlogin.png" @click="wxLogin"></image>
				<image src="@/static/images/apple-login.png" @click="appleLogin" v-if="platform == 'ios' || platform == 'macos'"></image>
			</view>
		</view>
		<view class="agreement">
			<radio :checked="agreement" color="#46B4B1" style="transform:scale(0.7);" @click="agreement=!agreement" />
			<text class="normal">我已阅读并同意</text>
			<text class="link" @click="handleAgreementDetail(1)">服务协议</text>
			<text class="normal">和</text>
			<text class="link" @click="handleAgreementDetail(2)">隐私政策</text>
			<text class="normal">我已阅读并同意</text>
		</view>
		<DefineDialog class="submit-dialog" v-if="submitDialog" @cancel="cancelSubmit" @confirm="confirmSubmit">
			<view class="title">服务协议及隐私保护</view>
			<view class="desc">
				<p class="normal">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;欢迎使用囊中对比，我们非常重视您的个人信息和隐私保护，在您使用囊中对比之前，请仔细阅读
				<text class="link" @click="handleAgreementDetail(1)">服务协议</text>
				和
				<text class="link" @click="handleAgreementDetail(2)">隐私政策</text>
				我们将按照您同意的条款使用您的个人信息，以便给您提供服务。
				</p>
			</view>
		</DefineDialog>
	</view>
</template>

<script>
	import {
		login
	} from '@/api/index';
	import {
		toLogin
	} from '@/utils/util';
	export default {
		data() {
			return {
				platform: '',
				loginType: "0",
				agreement: false,
				univerifyManager: null,
				nextUrl: "/pages/home/<USER>",
				submitDialog: false,
				loadingFlag: false,
				account: '',
				password: '',
				accountFocus: false,
				passwordFocus: false,
				errors: {
					account: '',
					password: ''
				},
				eye: false
			}
		},
		computed: {
			iconSrc() {
				return this.eye ? '/static/images/preview-open.png' : '/static/images/preview-close.png';
			},
		},
		methods: {
			onSubmit(e) {
				this.loginType = "0";
				if (!this.agreement) {
					this.submitDialog = true;
					return;
				}
				if (this.validateForm()) {
					this.submitForm();
				}
			},
			
			

			handleFocus(type) {
				this.accountFocus = false;
				this.passwordFocus = false;
				if (type == 'account') {
					this.accountFocus = true;
					this.errors.account = '';
				} else if (type == 'password') {
					this.passwordFocus = true;
					this.errors.password = '';
				}
			},
			
			bindTextInput(e, type) {
				if(type === 'account') {
					this.account = e.detail.value;
				} else if(type === 'password') {
					this.password = e.detail.value;
				}
			},

			validateForm() {
				this.errors = {}; // 清除错误信息
				const account = this.account.trim();
				const password = this.password.trim();
				// 验证邮箱格式
				const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
				if (!emailRegex.test(account)) {
					this.errors.account = '请输入正确的邮箱地址';
				}
				// 密码复杂度验证（这里只是一个简单的示例）
				if (!password || password.length < 6) {
					this.errors.password = '密码长度至少为6位';
				}
				return Object.keys(this.errors).length === 0;
			},

			submitForm() {
				this.loadingFlag = true;
				let filter = {
					account: this.account,
					password: this.password,
					type: this.loginType
				}
				login(filter).then(res => {
					this.loadingFlag = false;
					if (res.code == 200) {
						uni.setStorageSync('auth_token', res.token);
						uni.setStorageSync('login_time', new Date());
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					} else {
						let options = {
							text: '账号不存在或密码错误'
						}
						this.$refs.toast.show(options);
					}
				}).catch(err => {
					console.log(err)
				});
			},

			handleOtherOper(type) {
				if (type == 1) {
					uni.navigateTo({
						url: "/pages/login/register"
					})
				} else if (type == 2) {
					uni.navigateTo({
						url: "/pages/login/forget"
					})
				}
			},

			wxLogin() {
				this.loginType = "1";
				if (!this.agreement) {
					this.submitDialog = true;
					return;
				}
				var that = this;
				uni.login({
					"provider": "weixin",
					success: function(res) {
						uni.showLoading({});
						uni.getUserInfo({
							provider: 'weixin',
							success: async function(info) {
								await toLogin(info.userInfo.unionId, that.loginType, that.nextUrl);
								uni.hideLoading({});
							},
							fail: function(err) {
								uni.hideLoading({});
								let options = {
									text: '获取信息异常'
								}
								that.$refs.toast.show(options);
							}
						})
					},
					fail: function(err) {
						let options = {
							text: "授权异常"
						}
						that.$refs.toast.show(options);
					}
				})
			},
			
			appleLogin() {
				this.loginType = "2";
				if (!this.agreement) {
					this.submitDialog = true;
					return;
				}
				var that = this;
				uni.login({
				    provider: 'apple',
				    success: function (loginRes) {
				        uni.getUserInfo({
				            provider: 'apple',
				            success: async function(info) {
								await toLogin(info.userInfo.openId, that.loginType, that.nextUrl);
				            },
							fail: function(err) {
								let options = {
									text: '获取信息异常'
								}
								that.$refs.toast.show(options);
							}
				        })
				    },
				    fail: function (err) {
				        let options = {
				        	text: "授权异常"
				        }
						console.log(err);
				        that.$refs.toast.show(options);
				    }
				});

			},

			cancelSubmit() {
				this.submitDialog = false;
			},

			confirmSubmit() {
				this.agreement = true;
				this.submitDialog = false;
				if (this.loginType == "0") {
					this.onSubmit();
				} else if (this.loginType == "1") {
					this.wxLogin();
				} else if(this.loginType == "2") {
					this.appleLogin();
				}
			},

			handleAgreementDetail(type) {
				if (type == 1) {
					uni.navigateTo({
						url: "/pages/service/agreement"
					})
				}
				if (type == 2) {
					uni.navigateTo({
						url: "/pages/service/privicy"
					})
				}
			}
		},

		mounted() {
			if (this.passwordFocus) {
				this.$nextTick(() => {
					this.$refs.passwordInput.focus();
				});
			}
		},

		onLoad(options) {
			uni.getSystemInfo().then(res => {
				this.platform = res.osName;
			});
			if (options.account) {
				this.account = options.account;
				this.$nextTick(() => {
					this.$refs.accountInput.value = this.account;
				});
				this.passwordFocus = true;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		width: 100%;
		position: relative;
		align-items: center;
		height: 100%;
	}

	.title {
		font-size: $uni-font-size-lm;
	}

	.input-group {
		display: flex;
		align-items: center;
		margin-top: 60rpx;
		width: 684rpx;
		background-color: $uni-bg-color-grey;
		padding: 0 20rpx;
		height: 90rpx;
		box-sizing: border-box;
		border-radius: 14rpx;

		& .icon {
			margin-right: 10px;
			width: 36rpx;
			height: 36rpx;
		}

		& .input {
			flex: 1;
			border: none;
			outline: none;
			background-color: transparent;
			color: inherit;
			padding: 0;
			margin: 0;
			width: 100%;
			height: 100%;
			box-sizing: border-box;
		
			&::placeholder {
				color: $uni-text-color-placeholder;
			}
		}
	}

	.input-focus {
		border: 1px solid $uni-color-primary;
	}

	.error {
		color: $uni-color-error;
		font-size: $uni-font-size-sm;
		padding: 0 36rpx;
		margin-top: 8rpx;
	}


	.submit-button {
		margin-top: 60rpx;
		width: 684rpx;
		background-color: $uni-color-primary;
		color: #fff;
		font-size: $uni-font-size-lg;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.other-oper {
		margin-top: 96rpx;
		display: flex;
		width: 684rpx;
		justify-content: space-around;
		font-size: $uni-font-size-base;
		color: $uni-color-primary;
	}

	.third-login {
		display: flex;
		flex-direction: column;
		margin-top: auto;
	}

	.third-login .title {
		font-size: $uni-font-size-sm;
		color: $uni-text-color-disable;
		display: flex;
		width: 100%;
		justify-content: center;
		align-items: center;
	}

	.third-login .title .border {
		border: 1rpx solid $uni-bg-color-grey;
		width: 140rpx;
		height: 0px;
		margin: 0 24rpx;
	}

	.third-login .login-type {
		display: flex;
		flex-direction: row;
		margin-top: 40rpx;
		display: flex;
		width: 100%;
		justify-content: space-around;
	}

	.third-login .login-type image {
		width: 72rpx;
		height: 72rpx;
	}

	.agreement {
		display: flex;
		flex-direction: row;
		margin-top: auto;
		align-items: center;
		font-size: $uni-font-size-sm;
		margin-bottom: 20rpx;
	}

	.agreement .normal {
		color: $uni-text-color-disable;
	}

	.agreement .link {
		text-decoration-line: underline;
		color: $uni-text-color;
		padding: 0 8rpx;
	}

	.submit-dialog .title {
		width: 100%;
		text-align: center;
		font-size: $uni-font-size-lm;
		margin-bottom: 32rpx;
	}
	
	.submit-dialog  .desc {
		color: $uni-text-color-disable;
		font-size: $uni-font-size-sm;
		padding: 0 28rpx;
	}
	
	.submit-dialog .desc .normal {
		color: $uni-text-color-disable;
	}
	
	.submit-dialog .desc .link {
		text-decoration-line: underline;
		color: $uni-color-primary;
		padding: 0 8rpx;
	}
</style>