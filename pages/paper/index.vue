<template>
	<load v-if="loadingFlag"></load>
	<view class="page-body">
		<view class="search">
			<image class="search-icon" src="@/static/images/search.png" />
			<input class="search-content" type='text' placeholder='搜索年份、试卷类型' @confirm="handleSearch"></input>
		</view>
		<scroll-view class="list" @scrolltolower="scrolltolower" lower-threshold="200" :scroll-y="true">
			<view class="list-item" @click="handleClickItem(item.paperId)" v-for="(item, key) in paperList" :key="key">
				<text class="item-left">
					{{item.title}}
				</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		getPaperList
	} from '@/api/index';
	export default {
		data() {
			return {
				loadingFlag: true,
				areaId: null,
				paperList: [],
				queryContent: '',
				hasMore: false,
				pageNum: 1,
				pageSize: 20,
			}
		},
		methods: {

			handleClickItem(paperId) {
				uni.navigateTo({
					url: '/pages/detail/index?paperId=' + paperId,
				})
			},

			handleSearch(e) {
				this.queryContent = e.detail.value;
				this.pageNum = 1;
				this.areaId = null;
				let filter = {
					title: this.queryContent
				};
				this.paperList = [];
				this.selectPaperList(filter);
			},

			scrolltolower() {
				if (this.hasMore) {
					let filter = {
						title: this.queryContent
					};
					if (this.areaId) {
						filter.areaId = this.areaId;
					}
					this.selectPaperList(filter);
				}
			},

			selectPaperList(filter) {
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getPaperList(filter).then(res => {
					let list = this.paperList.concat(res.rows);
					let nextPage = ++this.pageNum;
					if (list.length < res.total) {
						this.hasMore = true;
					} else {
						this.hasMore = false;
					}
					this.paperList = list;
					this.pageNum = nextPage;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},
		},

		onLoad(opations) {
			this.areaId = opations.areaId;
			var filter = {
				areaId: this.areaId
			}
			this.selectPaperList(filter);
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.search {
		width: 100%;
		height: 70rpx;
		display: flex;
		flex-direction: row;
		border-radius: 32rpx;
		background: rgba(70, 180, 177, 0.1);
		align-items: center;
	}

	.search .search-icon {
		margin-left: 20rpx;
		width: 32rpx;
		height: 32rpx;
	}

	.search .search-content {
		margin-left: 20rpx;
		width: 85%;
		color: $uni-text-color-grey;
		font-size: 28rpx;
	}

	.list {
		width: 100%;
		margin-top: 20rpx;
		position: relative;
		flex: 1;
		height: 1px;
	}

	.list .list-item {
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		border-radius: 20rpx;
		min-height: 100rpx;
		background: rgba(70, 180, 177, 0.1);
	}

	.list .list-item .item-left {
		padding: 0 28rpx;
		font-size: 28rpx;
	}
</style>