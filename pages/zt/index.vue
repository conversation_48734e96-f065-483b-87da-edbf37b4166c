<template>
	<view class="page-body">
		<view class="options">
			<view class="option">
				<text class="title">字帖设置</text>
			</view>
			<view class="option">
				<text class="label">选择字体</text>
				<view class="type-select">
					<view @click="selectFont('kt')" :class="{'active': activeFont === 'kt'}">楷体</view>
					<view @click="selectFont('xs')" :class="{'active': activeFont === 'xs'}">行书</view>
					<!-- <view @click="selectFont('xk')" :class="{'active': activeFont === 'xk'}">行楷</view> -->
				</view>
			</view>
			<view class="option">
				<text class="label">选择试卷</text>
				<input class="search-content" type='text' placeholder='请选择试卷'
					v-model="paperTitle" @confirm="handleSearchPaperList" />
				<view class="paper-list" v-if="paperListShow">
					<scroll-view class="paper-list-wrapper" scroll-y bindscrolltolower="loadMore" show-scrollbar="{{false}}"
						enhanced="{{true}}">
						<view class="paper-item" @click="handleClickItem(item)" v-for="(item, index) in paperList"
							:key="index">{{item.title}}</view>
						<view class="paper-item" v-if="paperList.length == 0" style="display:flex;justify-content: center;">
							无数据</view>
					</scroll-view>
					<view class="close" @click="colose">收起</view>
				</view>
				<view class="answer-button" @click="handleSelectAnswer">选择答案</view>
			</view>
		</view>
		<view class="view">
			<view class="title-row">
				<text class="title">预览</text>
				<view class="clear-button" @click="clearContent">清空</view>
			</view>

			<!-- 字帖内容 - 滚动分页 -->
			<scroll-view
				class="view-content-scroll"
				scroll-y
				@scrolltolower="loadMoreContent"
				lower-threshold="200"
				:scroll-top="scrollTop"
				show-scrollbar="true">
				<view class="view-content">
					<template v-for="(pageIndex, pageKey) in displayedPages" :key="'page-container-'+pageKey">
						<view class="page-container">
							<view
								class="row"
								v-for="(_, key1) in row"
								:key="'row-'+pageKey+'-'+key1"
								:style="{'font-family': currentFont}">
								<view
									class="item"
									v-for="(_, key2) in cloum"
									:key="'item-'+pageKey+'-'+key1+'-'+key2">
									{{getContentAtPosition(pageIndex, key1, key2)}}
								</view>
							</view>
							<!-- 页码指示器 -->
							<view class="page-indicator">
								第 {{pageIndex}} 页
							</view>
						</view>
					</template>
				</view>
				<view class="loading-indicator" v-if="isLoading">
					<text>加载中...</text>
				</view>
			</scroll-view>
		</view>
		<view class="download-button" @click="canvasImage.download">
			下载字帖
		</view>
	</view>
</template>

<script>
	import {
		getPaperList
	} from '@/api/index';
	import {
		tokenStatus
	} from '@/utils/util';
	export default {
		data() {
			return {
				activeFont: 'kt',
				row: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],
				cloum: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19],
				content: ['选','择','答','案','后','，','即','可','看','到','预','览','效','果','。'],
				fonts: ['MyKaiti', 'MyXingshu','MyXingkai'],
				currentFont: 'MyKaiti',
				paperListShow: false,
				queryContent: '',
				pageNum: 1,
				pageSize: 20,
				paperList: [],
				paperId: null,
				paperTitle: '',
				// 滚动分页相关变量
				currentPage: 1,
				itemsPerPage: 625, // 25行 × 25列 = 625个字符
				displayedPages: [1], // 当前显示的页码数组
				isLoading: false,    // 是否正在加载更多内容
				scrollTop: 0,        // 滚动位置
			}
		},
		computed: {
			// 计算总页数
			totalPages() {
				return Math.ceil(this.content.length / this.itemsPerPage);
			}
		},
		methods: {
			selectFont(fontType) {
				this.activeFont = fontType;
				if(fontType == 'kt') {
					this.currentFont = this.fonts[0];
				} else if(fontType == 'xs') {
					this.currentFont = this.fonts[1];
				} else if(fontType == 'xk') {
					this.currentFont = this.fonts[2];
				}
				console.log(this.currentFont);
			},

			handleClickItem(item) {
				this.paperId = item.paperId;
				this.paperTitle = item.title;
				this.paperListShow = false;
				let filter = {
					paperId: item.paperId,
				}
				// this.selectQuestionList(filter);
			},

			handleSearchPaperList(e) {
				this.queryContent = e.detail.value;
				this.pageNum = 1;
				let filter = {
					title: this.queryContent
				};
				this.paperList = [];
				console.log(e);
				this.selectPaperList(filter);
			},

			selectPaperList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getPaperList(filter).then(res => {
					this.paperListShow = true;
					this.paperList = res.rows;
				}).catch(err => {
					console.log(err)
				});
			},

			colose() {
				this.paperListShow = false;
			},
			handleSelectAnswer() {
				// 如果没有选择试卷，提示用户先选择试卷
				if (!this.paperId) {
					uni.showToast({
						title: '请先选择试卷',
						icon: 'none'
					});
					return;
				}

				// 导航到答案选择页面，添加fromZt参数
				uni.navigateTo({
					url: '/pages/detail/index?paperId=' + this.paperId + '&paperTitle=' + this.paperTitle + '&fromZt=true'
				});
			},
			// 添加接收返回答案的方法
			onAnswerSelected(content) {
				// 处理HTML内容，转换为纯文本
				const cleanContent = content
					.replace(/<[^>]+>/g, '')         // 移除所有HTML标签
					.replace(/&nbsp;/g, ' ')         // 将&nbsp;转换为空格
					.replace(/&amp;/g, '&')          // 处理&符号
					.replace(/&lt;/g, '<')           // 处理<符号
					.replace(/&gt;/g, '>')           // 处理>符号
					.replace(/&quot;/g, '"')         // 处理"符号
					.replace(/&apos;/g, "'")         // 处理'符号
					.replace(/\s+/g, ' ')            // 将多个空白字符合并为一个空格
					.trim();                          // 移除首尾空白

				// 将答案内容转换为字符数组
				this.content = cleanContent.split('');

				// 重置分页相关变量
				this.currentPage = 1;
				this.displayedPages = [1]; // 重置为只显示第一页
				this.scrollTop = 0;        // 滚动回顶部

				// 更新视图
				this.$forceUpdate();
			},
			// 获取指定位置的内容
			getContentAtPosition(pageIndex, row, col) {
				const index = ((pageIndex - 1) * this.itemsPerPage) + (row * 25 + col);
				return index < this.content.length ? this.content[index] : '';
			},
			// 上一页
			prevPage() {
				if (this.currentPage > 1) {
					this.currentPage--;
				}
			},
			// 下一页
			nextPage() {
				if (this.currentPage < this.totalPages) {
					this.currentPage++;
				}
			},

			// 加载更多内容（滚动到底部时触发）
			loadMoreContent() {
				// 如果已经加载了所有页面，或者正在加载中，则不执行
				if (this.displayedPages.length >= this.totalPages || this.isLoading) {
					return;
				}

				// 设置加载状态
				this.isLoading = true;

				// 模拟加载延迟（实际应用中可能不需要）
				setTimeout(() => {
					// 计算下一页的页码
					const nextPage = this.displayedPages.length + 1;

					// 添加到已显示页面数组
					if (nextPage <= this.totalPages) {
						this.displayedPages.push(nextPage);
					}

					// 重置加载状态
					this.isLoading = false;
				}, 300);
			},


			async downloadCopybook(doc) {
				if (!this.content || this.content.length === 0) {
					uni.showToast({
						title: '请先选择内容',
						icon: 'none'
					});
					return;
				}

				// 显示加载提示
				uni.showLoading({
					title: '正在生成PDF...'
				});

				// 动态加载库

				try {

					// 确保displayedPages已初始化
					if (!this.displayedPages || !Array.isArray(this.displayedPages)) {
						this.displayedPages = [1];
					}

					// 确保所有页面都已加载
					// 如果当前显示的页面数量小于总页数，先加载所有页面
					const originalDisplayedPages = [...this.displayedPages];

					// 确保totalPages已计算
					const totalPages = this.totalPages || Math.ceil(this.content.length / this.itemsPerPage) || 1;

					// 加载所有页面
					this.displayedPages = Array.from(
						{ length: totalPages },
						(_, i) => i + 1
					);

					// 等待DOM更新
					await this.$nextTick();

					// 等待一点时间确保DOM完全渲染
					await new Promise(resolve => setTimeout(resolve, 500));

				} catch (err) {
					console.error('PDF生成失败:', err);
					// 输出更详细的错误信息以便调试
					console.error('错误详情:', {
						contentLength: this.content ? this.content.length : 0,
						displayedPages: this.displayedPages,
						totalPages: this.totalPages,
						error: err.toString(),
						stack: err.stack
					});

					// 隐藏加载提示
					uni.hideLoading();
					uni.showToast({
						title: 'PDF生成失败: ' + err.message,
						icon: 'none',
						duration: 3000
					});
				}
			},

			/**
			 * base64字符串转成文件
			 * @param {String} base64Str // 允许包含前缀
			 * @param {String} fileName // 文件名称：1663061363470.xlsx
			 */
			async base64ToFile(base64Str, fileName) {
				try {
				        // 1. 移除 Base64 前缀（如 "data:image/png;base64,"）
				        const cleanBase64 = base64Str.replace('data:application/pdf;filename=generated.pdf;base64,', '');
				        // 2. 检测运行环境
				        const { platform, OS } = uni.getSystemInfoSync();
						console.log(platform);
				        // ==== 3.1 H5 环境（浏览器）====
				        if (platform === 'h5') {
				            const blobUrl = this.getBlobUrlFromBase64(cleanBase64);
				            this.downloadFileInBrowser(blobUrl, fileName);
				            return { savedFilePath: blobUrl };
				        }
				        // ==== 3.2 App 或小程序环境 ====
				        else {
				            // 1）Base64 -> ArrayBuffer
				            const arrayBuffer = this.base64ToByteArray(cleanBase64);
				            // 2）App 端：使用 `plus.io` 写入文件
				            if (platform === 'android' || platform === 'ios') {
				                return await this.saveFileInApp(arrayBuffer, fileName, platform);
				            }
				            // 3）小程序端：通过 `uni.downloadFile` 模拟下载
				            else if (platform === 'mp-weixin' || platform === 'mp-alipay') {
				                return await this.saveFileInMiniProgram(arrayBuffer, fileName);
				            }
				            throw new Error('当前平台不支持');
				        }
				    } catch (err) {
				        console.error('保存失败:', err);
				        throw err;
				    }
			},

			// --- H5 环境辅助方法 ---
			getBlobUrlFromBase64(base64) {
			    const binaryString = atob(base64);
			    const bytes = new Uint8Array(binaryString.length);
			    for (let i = 0; i < binaryString.length; i++) {
			        bytes[i] = binaryString.charCodeAt(i);
			    }
			    const blob = new Blob([bytes.buffer], { type: 'application/octet-stream' });
			    return URL.createObjectURL(blob);
			},

			downloadFileInBrowser(url, fileName) {
			    const link = document.createElement('a');
				console.log(link);
			    link.href = url;
			    link.download = fileName;
			    document.body.appendChild(link);
			    link.click();
			    document.body.removeChild(link);
			    URL.revokeObjectURL(url);
			},
			// --- App 环境辅助方法 ---
			async saveFileInApp(arrayBuffer, fileName, platform) {
				return new Promise((resolve, reject) => {
					if(platform === 'android') {
						plus.android.requestPermissions([
							'android.permission.WRITE_EXTERNAL_STORAGE',
							'android.permission.READ_EXTERNAL_STORAGE',
							'android.permission.INTERNET',
							'android.permission.ACCESS_WIFI_STATE'
						], () => {
							const File = plus.android.importClass('java.io.File');
							let file = new File('/storage/emulated/0/Download/囊中对比');
							if (!file.exists()) { //文件夹不存在即创建
								file.mkdirs();
							}

							plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
								fs.root.getFile(fileName, {
									create: true
								}, function() {
									uni.hideLoading();
									// 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
									var fullPath = `/storage/emulated/0/Download/囊中对比/`+fileName;
									var FileOutputStream = plus.android.importClass("java.io.FileOutputStream");
									var out = new FileOutputStream(fullPath);
									out.write(arrayBuffer);
									out.close();
									resolve({ savedFilePath: fullPath });
								}, function(error) {
									reject(error);
								})
							}, function(error) {
								reject(error);
							});
						}, () => {
							uni.showToast({
								title: '无法获取权限，文件下载将出错！',
								icon: 'none',
							});
							reject(new Error('权限获取失败'));
						});
					} else if(platform === 'ios') {
						try {
							// 使用plus.io API来处理iOS文件操作
							plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
								// 直接在应用的Documents目录下创建文件
								fs.root.getFile(fileName, {create: true}, function(fileEntry) {
									// 创建文件写入器
									fileEntry.createWriter(function(writer) {
										// 将arrayBuffer转换为Uint8Array（如果它是普通数组的话）
										let dataToWrite;
										if (Array.isArray(arrayBuffer)) {
											// 如果是普通数组，转换为Uint8Array
											dataToWrite = new Uint8Array(arrayBuffer);
											console.l
										} else {
											// 如果已经是ArrayBuffer或Uint8Array，直接使用
											dataToWrite = arrayBuffer;
										}

										// 创建Blob对象
										const blob = new Blob([dataToWrite], {type: 'application/pdf'});

										writer.onwriteend = function() {
											uni.hideLoading();
											console.log('iOS文件保存成功:', fileEntry.fullPath);

											// 成功提示
											uni.showToast({
												title: 'PDF保存成功',
												icon: 'success',
												duration: 2000
											});

											// 询问是否打开文件
											uni.showModal({
												title: '保存成功',
												content: `文件已保存到应用Documents目录\n文件名: ${fileName}\n是否要打开文件？`,
												confirmText: '打开',
												cancelText: '取消',
												success: function(modalRes) {
													if (modalRes.confirm) {
														// 使用系统默认应用打开PDF文件
														plus.runtime.openFile(fileEntry.fullPath, {}, function() {
															console.log('文件打开成功');
														}, function(openError) {
															console.error('文件打开失败:', openError);
															uni.showToast({
																title: '无法打开文件',
																icon: 'none'
															});
														});
													}
												}
											});

											resolve({ savedFilePath: fileEntry.fullPath });
										};

										writer.onerror = function(writeError) {
											uni.hideLoading();
											console.error('iOS文件写入失败:', writeError);

											// 错误提醒
											uni.showModal({
												title: '保存失败',
												content: `文件写入失败: ${writeError.message || '未知错误'}`,
												showCancel: false
											});

											reject(writeError);
										};

										// 写入文件前的调试信息
										console.log('准备写入文件，原始数据类型:', typeof arrayBuffer);
										console.log('转换后数据类型:', typeof dataToWrite);
										console.log('数据长度:', dataToWrite.length);
										console.log('数据前10个字节:', Array.from(dataToWrite.slice(0, 10)));
										console.log('Blob对象:', blob);
										// 写入文件
										writer.write(blob);
									}, function(writerError) {
										uni.hideLoading();
										console.error('创建文件写入器失败:', writerError);

										uni.showModal({
											title: '保存失败',
											content: `创建文件写入器失败: ${writerError.message || '未知错误'}`,
											showCancel: false
										});

										reject(writerError);
									});
								}, function(fileError) {
									uni.hideLoading();
									console.error('创建文件失败:', fileError);

									uni.showModal({
										title: '保存失败',
										content: `创建文件失败: ${fileError.message || '未知错误'}`,
										showCancel: false
									});

									reject(fileError);
								});
							}, function(fsError) {
								uni.hideLoading();
								console.error('请求文件系统失败:', fsError);

								uni.showModal({
									title: '保存失败',
									content: `请求文件系统失败: ${fsError.message || '未知错误'}`,
									showCancel: false
								});

								reject(fsError);
							});
						} catch (err) {
							uni.hideLoading();
							console.error("iOS文件保存失败: ", err);

							// 错误提醒（UNI-APP样式）
							uni.showModal({
								title: '保存失败',
								content: `保存失败: ${err.message || '未知错误'}`,
								showCancel: false
							});

							reject(err);
						}
					} else {
						reject(new Error('不支持的平台'));
					}
				});
			},

			// --- 小程序环境辅助方法（如微信小程序）---
			async saveFileInMiniProgram(arrayBuffer, fileName) {
			    const tempFilePath = `${wx.env.USER_DATA_PATH}/${Date.now()}_${fileName}`;
			    await wx.getFileSystemManager().writeFile({
			        filePath: tempFilePath,
			        data: arrayBuffer,
			        encoding: 'binary',
			    });
			    return { savedFilePath: tempFilePath };
			},


			base64ToByteArray(base64Str) {
				const binaryString = atob(base64Str);
				const uint8Array = new Uint8Array(binaryString.length);

				for (let i = 0; i < binaryString.length; i++) {
					uint8Array[i] = binaryString.charCodeAt(i);
				}
				let arr = []
				Array.from(uint8Array).map(num => {
					arr.push(num >= 128 ? (num - 256) : num)
				})
				return arr;
			},

			downPdf(base64Str) {
				const fileName = `字帖-${new Date().getTime()}.pdf`;
				this.base64ToFile(base64Str, fileName).then(() => {
					// 隐藏加载提示
					uni.hideLoading();
					uni.showToast({
						title: 'PDF生成成功',
						icon: 'success'
					});
				}).catch((error) => {
					uni.hideLoading();
					console.error('PDF保存失败:', error);
					uni.showToast({
						title: 'PDF保存失败',
						icon: 'none'
					});
				});
			},

			// 添加清空内容方法
			clearContent() {
				// 显示确认对话框
				uni.showModal({
					title: '确认清空',
					content: '确定要清空所有内容吗？',
					success: (res) => {
						if (res.confirm) {
							// 用户点击确定，清空内容
							this.content = [];
							this.currentPage = 1;
							this.displayedPages = [1]; // 重置为只显示第一页
							this.scrollTop = 0;        // 滚动回顶部
							uni.showToast({
								title: '已清空',
								icon: 'success',
								duration: 1500
							});
						}
					}
				});
			}
		},
		mounted() {
		},
		onLoad(options) {
			// 添加页面返回事件监听
			uni.$on('ztAnswerSelected', this.onAnswerSelected);
		},
		onUnload() {
			uni.$off('ztAnswerSelected');
		}
	}
</script>

<script lang="renderjs" module = "canvasImage">
	import { jsPDF } from 'jspdf';
	import html2canvas from 'html2canvas';
	export default {
		methods: {
			async download() {
				let doc = new jsPDF('p', 'mm', 'a4');
				await this.$ownerInstance.callMethod('downloadCopybook');
				const pageContainers = document.querySelectorAll('.page-container');
				console.log('找到页面容器数量:', pageContainers.length);
				// 获取所有页面容器
				if (pageContainers.length === 0) {
					// 尝试获取整个内容区域作为备选
					const contentElement = document.querySelector('.view-content');
					if (contentElement) {
						console.log('使用备选内容区域');
						// 使用整个内容区域生成单页PDF
						const canvas = await html2canvas(contentElement, {
							scale: 2,
							useCORS: true,
							allowTaint: true,
							logging: false
						});

						const imgWidth = 190;
						const imgHeight = (canvas.height * imgWidth) / canvas.width;
						const imgData = canvas.toDataURL('image/jpeg', 1.0);
						doc.addImage(imgData, 'JPEG', 10, 10, imgWidth, imgHeight);

						// 保存PDF
						// const fileName = `字帖-${new Date().getTime()}.pdf`;
						// doc.save(fileName);

						var blob = doc.output("datauristring");
						this.$ownerInstance.callMethod('downPdf', blob);
						return;
					} else {
						throw new Error('未找到页面内容');
					}

				}

				// 遍历每个页面并添加到PDF
				for (let i = 0; i < pageContainers.length; i++) {
					const pageContainer = pageContainers[i];

					// 使用html2canvas将内容转为canvas
					const canvas = await html2canvas(pageContainer, {
						scale: 2, // 提高分辨率
						useCORS: true, // 允许跨域图片
						allowTaint: true, // 允许污染模式
						logging: false // 关闭日志
					});

					// 计算A4尺寸 (210mm x 297mm)
					const imgWidth = 190; // 留边距
					const imgHeight = (canvas.height * imgWidth) / canvas.width;

					// 添加图片到PDF
					const imgData = canvas.toDataURL('image/jpeg', 1.0);

					// 如果不是第一页，先添加新页
					if (i > 0) {
						doc.addPage();
					}

					doc.addImage(imgData, 'JPEG', 10, 10, imgWidth, imgHeight);
				}

				// 保存PDF

				var blob = doc.output("datauristring");
				this.$ownerInstance.callMethod('downPdf', blob);
			}
		}
	}
</script>
<style lang="scss" scoped>
	/* 定义字体 */
	@font-face {
	  font-family: "MyKaiti";
	  src: url("/static/fonts/STKaiti.ttf") format("truetype");
	}
	@font-face {
	  font-family: "MyXingshu";
	  src: url("/static/fonts/STXingshu.ttf") format("truetype");
	}
	// @font-face {
	//   font-family: "MyXingkai";
	//   src: url("/static/fonts/STXingkai.ttf") format("truetype");
	// }
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		padding: 0 28rpx;
		height: calc(100vh - 230rpx);
		overflow: scroll;
		padding-bottom: 28rpx;
		.options {
			margin-top: 28rpx;
			padding: 28rpx;
			width: calc(100% - 76rpx);
			border-radius: 28rpx;
			background-color: #fff;
			box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1),0px 4px 6px -1px rgba(0, 0, 0, 0.1);
			.option {
				display: flex;
				align-items: center;
				position: relative;
				margin-bottom: 32rpx;
				flex-wrap: wrap; /* 允许内容换行 */
				&:last-child {
					margin-bottom: 0;
				}
				.title {
					font-size: 36rpx;
					font-weight: bold;
				}
				.label {
					font-weight: bold;
					margin-right: 36rpx;
					white-space: nowrap; /* 防止标签文字换行 */
				}

				.search-content {
					border: 2rpx solid #d1d5db;
					color: $uni-text-color-grey;
					border-radius: 8rpx;
					padding: 6rpx 24rpx;
					flex: 1; /* 使输入框占据剩余空间 */
					min-width: 200rpx; /* 设置最小宽度 */
					max-width: calc(100% - 300rpx); /* 设置最大宽度，留出按钮空间 */
					margin-right: 20rpx; /* 与按钮保持间距 */
				}

				.answer-button {
					margin-left: auto; /* 将按钮推到右侧 */
					background-color: $uni-color-primary;
					color: #fff;
					padding: 6rpx 35rpx;
					border-radius: 10rpx;
					font-size: 28rpx;
					white-space: nowrap; /* 防止按钮文字换行 */
				}

				/* 在小屏幕上调整布局 */
				@media screen and (max-width: 375px) {
					.search-content {
						max-width: calc(100% - 240rpx);
					}
				}
				.type-select {
					display: flex;
					view {
						margin-right: 60rpx;
						border: 2rpx solid #d1d5db;
						padding: 6rpx 35rpx;
						border-radius: 10rpx;
					}
				}
				.active {
					background-color: $uni-color-primary;
					color: #fff;
				}
				.paper-list {
					position: absolute;
					width: 100%;
					top: 75rpx;
					z-index: 9999;
					background-color: $uni-bg-color;
					box-shadow: 0rpx 8rpx 12rpx 0rpx rgba(0, 0, 0, 0.25);
					border-radius: 20rpx;
					.close {
						border-top: 1px solid #e5e6eb;
						display: flex;
						justify-content: center;
						align-items: center;
						height: 80rpx;
						position: relative;
						font-size: $uni-font-size-base;
					}
					.paper-item:active {
						background-color: #eee;
					}
					.paper-item {
						padding: 20rpx 40rpx;
						font-size: $uni-font-size-base;
					}
				}
				.label {
					font-weight: bold;
					margin-right: 36rpx;
				}
				.answer-button {
					margin-left: auto;
					background-color: $uni-color-primary;
					color: #fff;
					padding: 6rpx 35rpx;
					border-radius: 10rpx;
					font-size: 28rpx;
				}
				margin-bottom: 32rpx;
			}
		}

	.download-button {
		position: fixed;
		bottom: 20rpx;
		left: 98rpx;
		right: 98rpx;
		width: calc(100% - 196rpx);
		background-color: $uni-color-primary;
		color: #fff;
		padding: 20rpx 0;
		border-radius: 40rpx;
		text-align: center;
		font-size: 32rpx;
		z-index: 100;
	}

	.view {
			margin-top: 24rpx;
			padding: 28rpx;
			background: #fff;
			box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1),0px 4px 6px -1px rgba(0, 0, 0, 0.1);
			border-radius: 28rpx;
			width: calc(100% - 56rpx);
			display: flex;
			flex-direction: column;
			.title-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				.title {
					font-size: 36rpx;
					font-weight: bold;
				}
				.clear-button {
					background-color: #ff6b6b;
					color: #fff;
					padding: 6rpx 20rpx;
					border-radius: 8rpx;
					font-size: 28rpx;
				}
			}
			.view-content-scroll {
				width: 100%;
			}

			.loading-indicator {
				text-align: center;
				padding: 20rpx 0;
				color: #999;
				font-size: 28rpx;
			}

			.page-container {
				position: relative;
				margin-bottom: 1rpx;
				padding-bottom: 20rpx;
				// border-bottom: 1px dashed #ddd;
			}

			.page-indicator {
				text-align: center;
				padding: 20rpx 0 10rpx;
				color: #666;
				font-size: 20rpx;
			}

			.row {
				display: flex;
				align-items: flex-end;

				.item {
					width: 100%;
					aspect-ratio: 7 / 8;
					border: 1rpx solid red;
					border-left: none;
					display: flex;
					justify-content: center;
					align-items: center;
					font-weight: normal;
					font-size: min(4vw,4vh);
					color: #ccc;
				}

				.mark {
					font-size: 16rpx;
					color: red;
				}

				:first-child {
					border-left: 1rpx solid red;
				}

				margin-bottom: 1%;
			}
		}
	}
</style>
