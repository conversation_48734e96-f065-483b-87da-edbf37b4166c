<template>
	<view class="page-body">
		<view class="result-header">
			<text class="title">{{paymentStatus == 'success' ? '支付成功' : '支付失败'}}</text>
		</view>
		<view class="result-content">
			<view v-if="paymentStatus == 'success'">
				<view class="status success">
					<image src="@/static/images/success.png" class="icon" />
				</view>
				<button type="primary" @click="handleFinish">完成支付</button>
			</view>
			<view v-else="paymentStatus == 'fail'">
				<view class="status fail">
					<image src="@/static/images/fail.png" class="icon" />
				</view>
				<button type="primary" @click="handleRetryPayment">重新支付</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				paymentStatus: 'fail',
			}
		},
		methods: {
			handleRetryPayment() {
				uni.navigateBack();
			},

			handleFinish() {
				uni.reLaunch({
					url: '/pages/mime/index',
				})
			}
		},
		onLoad(options) {
			const status = options.status;
			this.paymentStatus = status;
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.result-header {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.result-content {
		width: 90%;
		max-width: 600rpx;
		text-align: center;
	}

	.status {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
	}

	button[type="primary"] {
		margin-top: 100rpx;
		font-size: 28rpx;
		width: 300rpx;
		background-color: #70DEB1;
		border-color: #70DEB1;
	}
</style>