<template>
	<load v-if="loadingFlag"></load>
	<view class="page-body">
		<view class="search">
			<image class="search-icon" src="@/static/images/search.png" />
			<input class="search-content" type='text' placeholder='搜索省份、年份、试卷类型'
				@confirm="handleSearchPaperList"></input>
			<view class="paper-list" v-if="paperListShow">
				<scroll-view class="paper-list-wrapper" scroll-y bindscrolltolower="loadMore" show-scrollbar="{{false}}"
					enhanced="{{true}}">
					<view class="paper-item" @click="handleClickItem(item)" v-for="(item, index) in paperList"
						:key="index">{{item.title}}</view>
					<view class="paper-item" v-if="paperList.length == 0" style="display:flex;justify-content: center;">
						无数据</view>
				</scroll-view>
				<view class="close" @click="colose">收起</view>
			</view>
		</view>

		<view class="questions" v-if="questionShow">
			<view class="paper-title">{{paperTitle}}</view>
			<view class="section-list">
				<view @click="selectQuestion(item.questionIndex)" class="question-section"
					v-for="(item, index) in questionList" :key="index">
					<view class="title">
						第{{item.questionIndex}}题
					</view>
					<view v-if="questionIndex == item.questionIndex" class="show_bar">
					</view>
				</view>
			</view>
			<view class="question-context">
				<view class="title">
					<rich-text :nodes="questionContent">
					</rich-text>
				</view>
			</view>
		</view>
		<view class="agency-source">
			<input ref="inputRef" @input="handleAgenceSource" type='text' :placeholder='placeholder1'></input>
		</view>

		<view class="content">
			<richText ref="richText" :readOnly="readOnly" :placeholder="placeholder2"
				@onEditorReady='onEditorReady' @bindinput='bindinput'
			    @ocrScanEvent='ocrScanEvent'>
			</richText>
			<view class="length">（{{userAnswerContentLenght}}字）</view>
		</view>
		<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>
		<view class="form_btn_wrap">
			<view class="form-btn" @click="showSubmitDialog">
				<icon class="icon" type="success_no_circle" size="16" color="#fff">
				</icon>
				提交答案
			</view>
		</view>
		<view>
			<p class="add-desc" style="white-space:pre-line">
				1.每成功补充1道答案，将获得1天纯净版会员；
			</p>
			<p class="add-desc" style="white-space:pre-line">
				2.已有的4家机构和网友上传答案，不要重复上传；
			</p>
			<p class="add-desc" style="white-space:pre-line">
				3.不要补充自己写的答案，低质量答案会被定期删除；
			</p>	
		</view>
	</view>
</template>

<script>
	import {
		getPaperList,
		getQuestionList,
		insertUserAddAnswer,
		uploadMinioUrl
	} from '@/api/index';
	import {
		tokenStatus
	} from '@/utils/util';
	let richText = null;
	export default {
		data() {
			return {
				loadingFlag: false,
				queryContent: "",
				hasMore: true,
				pageNum: 1,
				pageSize: 1000,
				paperId: '',
				paperList: [],
				source: "",
				placeholder1: "请输入您的答案来源机构…",
				readOnly: false,
				placeholder2: "请输入答案内容",
				dialogContent: "答案审核通过后，将会对外开放，奖励会员天数*1随之发放",
				userAnswerContentLenght: 0,
				paperListShow: false,
				paperTitle: "",
				questionShow: false,
				questionIndex: 1,
				questionId: "",
				scanContent: "",
				questionContent: "",
				submitDialog: false
			}
		},
		methods: {
			handleSearchPaperList(e) {
				this.queryContent = e.detail.value;
				this.pageNum = 1;
				let filter = {
					title: this.queryContent
				};
				this.paperList = [];
				this.selectPaperList(filter);
			},
			handleAgenceSource(e) {
				let value = e.detail.value;
				this.source = value;
			},
			loadMore() {
				if (this.hasMore) {
					let filter = {
						title: this.queryContent
					};
					this.selectPaperList(filter);
				}
			},
			handleClickItem(item) {
				this.paperId = item.paperId;
				this.paperTitle = item.title;
				this.paperListShow = false;
				let filter = {
					paperId: item.paperId,
				}
				this.selectQuestionList(filter);
			},

			selectPaperList(filter) {
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getPaperList(filter).then(res => {
					this.paperListShow = true;
					this.paperList = res.rows;
				}).catch(err => {
					console.log(err)
				});
			},

			selectQuestionList(filter) {
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getQuestionList(filter).then(res => {
					this.questionList = res.rows;
					this.questionShow = true;
					this.handleQuestionShow();
				}).catch(err => {
					console.log(err)
				});
			},

			selectQuestion(index) {
				this.questionIndex = index;
				this.handleQuestionShow();
			},

			// 初始化编辑器
			onEditorReady() {
				richText = this.$refs.richText;
			},

			bindinput(e) {
				let value = e.value.detail.text.replace(/[(^*\n*)|(^*\r*)]/g, '');
				this.userAnswerContentLenght = value.length;
			},

			showSubmitDialog() {
				if (!this.questionId) {
					uni.showToast({
						title: '未选择试卷题目',
						icon: "error"
					});
					return;
				}
				if (!this.source) {
					uni.showToast({
						title: '未填写答案来源',
						icon: "error"
					});
					return;
				}
				if (this.userAnswerContentLenght > 0) {
					this.submitDialog = true;
				} else {
					uni.showToast({
						title: '未填写答案内容',
						icon: "error"
					});
				}
			},
			
			
			cancelSubmit() {
				this.submitDialog = false;
			},

			confirmSubmit() {
				let that = this;
				const tokenExist = tokenStatus();
				let userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
				if (!tokenExist) {
					uni.navigateTo({
						url: '/pages/login/index?url=/pages/answer-add/index',
					})
				} else {
					this.loadingFlag = true;
					richText.editorCtx.getContents({
						success: function(res) {
							let data = {};
							data.content = res.html;
							data.userId = userId;
							data.questionId = that.questionId;
							data.source = that.source;
							data.status = "0"; // 0代表未审核 1代表审核通过 2代表审核拒绝
							insertUserAddAnswer(data).then(res => {
								console.log(res);
								that.submitDialog = false;
								richText.editorCtx.setContents({
									html: '',
								});
								that.userAnswerContentLenght = 0;
								that.loadingFlag = false;
								uni.showToast({
									title: '操作成功',
									icon: "success"
								});
							}).catch(err => {
								console.log(err)
							});
						},
						fail: function(err) {
							console.log(err)
						}
					});
				}
			},
			handleQuestionShow() {
				let question = this.questionList[this.questionIndex - 1];
				this.questionId = question.questionId;
				this.questionContent = question.content;
			},

			ocrScanEvent() {
				uni.chooseImage({
					count: 1,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					success: res => {
						let item = res.tempFiles[0];
						let tempFilePath = item.path;
						var tokenKey = "auth_token";
						let token = uni.getStorageSync(tokenKey);
						uni.uploadFile({
							url: uploadMinioUrl,
							filePath: tempFilePath,
							name: 'file',
							formData: {},
							header: {
								'Authorization': token
							},
							success: res => {
								let data = JSON.parse(res.data);
								var imgUrl = data.url;
								uni.navigateTo({
									url: '/pages/ocr/index?imgUrl=' + imgUrl +
										'&paperId=' + this.paperId + '&paperTitle=' + this
										.paperTitle + '&questionIndex=' + this
										.questionIndex + '&from=answer-add' + '&source=' +
										this.source
								})
							}
						})
					}
				})
			},

			colose() {
				this.paperListShow = false;
			},
		},
		onLoad(options) {
			let paperId = options.paperId;
			let paperTitle = options.paperTitle;
			let scanContent = options.scanContent;
			let source = options.source;
			let questionIndex = options.questionIndex;
			let ocrParamStr = options.ocrParam;
			if (ocrParamStr) {
				let ocrParam = JSON.parse(decodeURIComponent(ocrParamStr));
				scanContent = ocrParam.scanContent;
				setTimeout(() => {
					richText.editorCtx.insertText({
						text: ocrParam.scanContent
					});
				}, 500);
				paperId = ocrParam.paperId;
				questionIndex = ocrParam.questionIndex;
				paperTitle = ocrParam.paperTitle;
				source = ocrParam.source;
			}
			this.paperId = paperId ? paperId : '';
			this.paperTitle = paperTitle;
			this.questionIndex = questionIndex ? questionIndex : 1;
			this.scanContent = scanContent ? scanContent : '';
			this.source = source ? source : '';
			this.$nextTick(() => {
				this.$refs.inputRef.value = this.source;
			});
			this.userAnswerContentLenght = scanContent ? scanContent.replace(/\s/g, '').length : 0;
			if (paperId) {
				let filter = {
					paperId: paperId,
				}
				this.selectQuestionList(filter);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		padding: 0 28rpx;
	}

	.search {
		width: 100%;
		height: 70rpx;
		display: flex;
		flex-direction: row;
		border-radius: 32rpx;
		background: rgba(70, 180, 177, 0.1);
		align-items: center;
		position: relative;
	}

	.search .search-icon {
		margin-left: 20rpx;
		width: 32rpx;
		height: 32rpx;
	}

	.search .search-content {
		margin-left: 20rpx;
		width: 85%;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
	}

	.search .paper-list {
		position: absolute;
		width: 100%;
		top: 75rpx;
		z-index: 9999;
		background-color: $uni-bg-color;
		box-shadow: 0rpx 8rpx 12rpx 0rpx rgba(0, 0, 0, 0.25);
		border-radius: 20rpx;
	}

	.search .paper-list .close {
		border-top: 1px solid #e5e6eb;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
		position: relative;
		font-size: $uni-font-size-base;
	}

	.search .paper-list .paper-item:active {
		background-color: #eee;
	}

	.search .paper-list .paper-item {
		padding: 20rpx 40rpx;
		font-size: $uni-font-size-base;
	}

	.search .paper-list .paper-list-wrapper {
		height: auto;
		max-height: 400rpx;
		overflow-y: scroll;
	}

	.questions {
		width: 100%;
	}

	.questions .section-list {
		display: flex;
		justify-content: space-between;
		align-items: center;
		overflow-x: scroll;
		padding-bottom: 16rpx;	
	}

	.questions .section-list .question-section {
		display: flex;
		flex-direction: column;
	}

	.questions .section-list .question-section .title {
		display: flex;
		padding: 10rpx 20rpx;
		height: 35rpx;
		flex-direction: row;
		justify-content: center;
		color: $uni-color-primary;
		font-size: $uni-font-size-base;
		width: 100rpx;
	}

	.questions .section-list .question-section .show_bar {
		display: flex;
		height: 4rpx;
		border-radius: 20rpx;
		background: $uni-color-primary;
		width: 40%;
		margin: 0 30%;
	}

	.questions .section-list .question-section .hide_bar {
		display: none;
		height: 4rpx;
		border-radius: 20rpx;
		background: $uni-color-primary;
		width: 40%;
		margin: 0 30%;
	}

	.question-context {
		margin-top: 20rpx;
		height: auto;
		padding: 12rpx 24rpx;
		overflow-y: scroll;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);

	}

	.question-context .title {
		display: flex;
		flex-direction: column;
		justify-content: center;
		color: $uni-text-color;
		font-size: 24rpx;
		line-height: 40rpx;
	}

	.paper-title {
		font-size: $uni-font-size-base;
		height: auto;
		display: flex;
		justify-content: center;
		align-items: center;
		color: $uni-color-primary;
		margin-top: 20rpx;
	}

	.agency-source {
		width: 100%;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
		display: flex;
		align-items: center;
		margin-top: 20rpx;
	}

	.agency-source input {
		padding: 0 40rpx;
		width: 100%;
		height: 70rpx;
		font-size: $uni-font-size-base;
	}

	.content {
		width: calc(100% - 56rpx);
		margin-top: 20rpx;
		position: relative;
		padding-top: 0rpx;
		padding: 28rpx;
		font-size: $uni-font-size-base;
		letter-spacing: 1rpx;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
	}

	.content .length {
		position: relative;
		color: $uni-color-primary;
		float: right;
		text-align: center;
		font-size: 24rpx;
	}

	.form_btn_wrap {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
	}

	.form-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		align-content: center;
		width: 320rpx;
		height: 88rpx;
		border-radius: 128rpx;
		background: $uni-color-primary;
		color: $uni-bg-color;
		text-align: center;
		font-size: 40rpx;
		margin-bottom: 20rpx;
	}
	
	.add-desc {
	    font-size: 28rpx;
	    font-weight: 400;
	    text-align: left;
	    color: red;
	}

	.form-btn .icon {
		margin-right: 20rpx;
	}

</style>