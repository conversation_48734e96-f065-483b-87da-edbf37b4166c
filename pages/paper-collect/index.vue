<template>
	<load v-if="loadingFlag"></load>
	<empty type="1" desc = '无收藏试卷,快去收藏自己关注的试卷吧!' :data = 'paperCollectList'></empty>
	<view class="page-body">
		<scroll-view class="list" :scroll-y="true" lower-threshold="200" @scrolltolower="scrolltolower">
			<view v-for="(item, index) in paperCollectList" :key="index">
				<view class="list-item">
					<view class="item-left" @click="handleClickPaper(item.paperId)">
						<view class="item-title">{{item.title}}</view>
					</view>
					<image class="delete" src="@/static/images/delete.png" @click="deleteRecord(['0',3,1,item.paperId])" />
				</view>
			</view>
		</scroll-view>
		<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>
	</view>
</template>

<script>
	import {
		getCollectPaperList,
		doAction
	} from '@/api/index';
	export default {
		data() {
			return {
				loadingFlag: true,
				paperCollectList: [],
				hasMore: false,
				pageNum: 1,
				pageSize: 20,
				params: {},
				submitDialog: false,
				dialogContent: '此操作执行后将无法撤回'
			}
		},
		methods: {
			selectCollectPaperList(filter) {
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getCollectPaperList(filter).then(res => {
					let list = this.paperCollectList.concat(res.rows);
					let nextPage = ++this.pageNum;
					if (list.length < res.total) {
						this.hasMore = true;
					} else {
						this.hasMore = false;
					}
					this.paperCollectList = list;
					this.pageNum = nextPage;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},

			confirmSubmit() {
				let data = {
					status: this.params[0],
					actionType: this.params[1],
					targetType: this.params[2],
					targetId: this.params[3]
				};
				doAction(data).then(res => {
					if (res.code == 200) {
						let newData = this.paperCollectList.filter(item => item.paperId !== data.targetId);
						this.paperCollectList = newData;
						this.submitDialog = false;
						uni.showToast({
							title: '取消收藏成功',
							icon: "success"
						});

					}
				});
			},

			deleteRecord(params) {
				this.params = params;
				this.submitDialog = true;
			},

			cancelSubmit() {
				this.submitDialog = false;
			},
			scrolltolower() {
				if (this.hasMore) {
					this.selectCollectPaperList({});
				}
			},
			handleClickPaper(paperId) {
				uni.navigateTo({
					url: '/pages/detail/index?paperId=' + paperId,
				})
			},
		},
		onLoad(options) {
			this.selectCollectPaperList({});
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.list {
		width: 100%;
		margin: 20rpx 0rpx;
		flex: 1;
		height: 1px;
	}

	.list .delete {
		width: 44rpx;
		height: 44rpx;
	}

	.list .list-item {
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		width: 100%;
	}

	.list .list-item .item-left {
		position: relative;
		display: flex;
		font-size: 28rpx;
		flex-direction: column;
		justify-content: center;
		width: 88%;
		padding-left: 24rpx;
		min-height: 100rpx;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
	}

	.list .list-item .delete {
		margin-left: auto;
	}
</style>