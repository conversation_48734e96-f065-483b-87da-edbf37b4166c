<template>
	<load v-if="loadingFlag"></load>
	<empty type="1" desc = '无随手记录,快去添加一条笔记吧!' :data = 'userNoteList'></empty>
	<view class="page-body">
		<scroll-view class="list" :scroll-y="true" lower-threshold="200" @scrolltolower="scrolltolower">
			<view v-for="(item, index) in userNoteList" :key="index">
				<view class="list-item">
					<view class="item-update-time">
						<image class="item-time-dot" src="@/static/images/time-dot.png" />
						最新修改于{{item.updateTime ? item.updateTime : item.createTime}}
					</view>
					<view class="item-header">
						<view class="item-title">{{item.paper.title}}</view>
						<view class="item-question-index">第{{item.question.questionIndex}}题</view>
						<image src="@/static/images/link-step.png"
							@click="handleClickPaper([item.paper.paperId,item.question.questionIndex])" />
					</view>
					<view class="item-content">
						<view class="content-main">
							{{item.content}}
						</view>
						<image class="delete" src="@/static/images/delete.png" @click="remove(item.noteId)" />
					</view>
				</view>
			</view>
		</scroll-view>
		<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>
	</view>
</template>

<script>
	import {
		getUserNoteList,
		deleteUserNote
	} from '@/api/index';
	export default {
		data() {
			return {
				loadingFlag: true,
				userNoteList: [],
				hasMore: false,
				pageNum: 1,
				pageSize: 20,
				submitDialog: false,
				dialogContent: '此操作执行后将无法撤回'
			}
		},
		methods: {
			selectUserNotes(filter) {
				this.hasMore = false;
				filter.pageSize = this.pageSize;
				filter.pageNum = this.pageNum;
				getUserNoteList(filter).then(res => {
					let list = this.userNoteList.concat(res.rows);
					let nextPage = ++this.pageNum;
					if (list.length < res.total) {
						this.hasMore = true;
					} else {
						this.hasMore = false;
					}
					this.userNoteList = list;
					this.pageNum = nextPage;
					this.loadingFlag = false;
				}).catch(err => {
					console.log(err)
				});
			},

			scrolltolower() {
				if (this.hasMore) {
					var userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
					if (userId) {
						var filter = {
							userId: userId,
						}
						this.selectUserNotes(filter);
					}
				}
			},

			handleClickPaper(params) {
				uni.navigateTo({
					url: '/pages/detail/index?paperId=' + params[0] + '&questionIndex=' + params[1],
				})
			},

			confirmSubmit() {
				deleteUserNote(this.noteId).then(res => {
					this.userNoteList = this.userNoteList.filter(item => item.noteId !== this.noteId);
					this.submitDialog = false;
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				}).catch(err => {
					console.log(err)
				});
			},

			remove(noteId) {
				this.noteId = noteId;
				this.submitDialog = true;
			},

			cancelSubmit() {
				this.submitDialog = false;
			},
		},
		onLoad(options) {
			var userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
			if (userId) {
				var filter = {
					userId: userId,
				}
				this.selectUserNotes(filter);
			}
		}
	}
</script>

<style lang="scss" scoped>

	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.list {
		width: 100%;
		margin: 20rpx 0rpx;
		flex: 1;
		height: 1px;
	}

	.list .list-item {
		position: relative;
		border-left: 4rpx dotted #676767;
		padding-left: 24rpx;
		margin-left: 16rpx;
		padding-bottom: 40rpx;
	}

	.list .list-item:last-child {
		padding-bottom: 0rpx;
	}

	.list .list-item .item-update-time {
		font-size: 24rpx;
		color: #676767;
		display: flex;
		justify-content: flex-start;
	}

	.list .list-item .item-update-time image {
		width: 32rpx;
		height: 32rpx;
		position: absolute;
		left: -18rpx;
	}

	.list .list-item .item-header {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 16rpx;
	}

	.list .list-item .item-header .item-title,
	.list .list-item .item-header .item-question-index {
		font-size: 28rpx;
		position: relative;
		color: #46B4B1;
	}

	.list .list-item .item-header .item-title {
		width: 70%;
	}

	.list .list-item .item-header image {
		width: 32rpx;
		height: 32rpx;
	}

	.list .list-item .item-content {
		display: flex;
		align-items: center;
		padding-bottom: 20rpx;
	}

	.list .list-item .item-content .content-main {
		background: rgba(70, 180, 177, 0.1);
		border-radius: 20rpx;
		margin-top: 16rpx;
		padding: 16rpx;
		font-size: 28rpx;
		letter-spacing: 1rpx;
		width: 85%;
	}

	.list .list-item .item-content .delete {
		width: 44rpx;
		height: 44rpx;
		margin-left: auto;
	}
</style>