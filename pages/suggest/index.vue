<template>
	<load v-if="loadingFlag"></load>
	<view class="page-body">
		<view class="suggest_content">
			<textarea ref="inputRef" maxlength="-1" @input="handleTextInput"
				placeholder="请详细说明建议内容,可附参考图"></textarea>
		</view>
		<view class="suggest_tool">
			<view class="title">
				<view class="icon">
					<image src="@/static/images/point.png" />
				</view>
				上传参考图片
			</view>
			<view class="up_img_list">
				<view class="up_img_item" v-for="(item, index) in imgSrcArr" v-key="index">
					<image class="content" :src="item.path"></image>
					<view class="close-wrapper" @click="closePicture(index)">
						<image class="close" src="@/static/images/close.png" />
					</view>
				</view>
				<view class="add-picture" @click="handleChooseImage">
					<view class="wrapper">
						<image src="@/static/images/add-picture.png" />
					</view>
					<text>添加照片</text>
				</view>
			</view>
		</view>
		<view class="form_btn_wrap">
			<view class="form-btn" @click="showSubmitDialog">
				<icon class="icon" type="success_no_circle" size="16" color="#fff">
				</icon>
				提交反馈
			</view>
		</view>
		<DefineDialog v-if="submitDialog" :content="dialogContent" @cancel="cancelSubmit" @confirm="confirmSubmit"></DefineDialog>
	</view>
</template>

<script>
	import {
		insertSuggest,
		uploadMinioUrl
	} from '@/api/index';
	import {
		formatTime
	} from '@/utils/util';
	export default {
		data() {
			return {
				loadingFlag: false,
				userId: null,
				imgSrcArr: [],
				textValue: '',
				submitDialog: false,
				dialogContent: '提交之后我们会看到你的建议'
			}
		},
		methods: {

			showSubmitDialog() {
				this.submitDialog = true;
			},

			cancelSubmit() {
				this.submitDialog = false;
			},

			handleTextInput(e) {
				this.textValue = e.detail.value;
			},

			async confirmSubmit() {
				this.loadingFlag = true;
				this.submitDialog = false;
				const imgPromises = [];
				let that = this;
				let data = {
					type: 2,
					content: this.textValue,
					userId: this.userId,
					imgs: '',
					create_time: formatTime(new Date()),
				};
				if (this.imgSrcArr.length == 0) {
					this.insertSuggest(data);
				} else {
					let imgs = '';
					for (let key in this.imgSrcArr) {
						let item = this.imgSrcArr[key];
						let tempFilePath = item.path;
						let tokenKey = "auth_token";
						let token = uni.getStorageSync(tokenKey);
						// 封装每个图片上传为一个 Promise
						imgPromises.push(new Promise((resolve, reject) => {
							uni.uploadFile({
								url: uploadMinioUrl,
								filePath: tempFilePath,
								name: 'file',
								formData: {},
								header: {
									'Authorization': token
								},
								success: res => {
									let resData = JSON.parse(res.data);
									var imgUrl = resData.url;

									// 图片上传成功后，将 URL 添加到 imgs 数组，并调用 resolve()
									imgs = imgs.concat(imgUrl).concat(",");
									resolve();
								},
								fail: err => {
									console.error(err);
									reject(err);
								}
							});
						}));
					}
					await Promise.all(imgPromises);
					data.imgs = imgs;
					that.insertSuggest(data);
				}
			},

			insertSuggest(data) {
				insertSuggest(data).then(res => {
					this.imgSrcArr = [];
					this.$refs.inputRef.value = '';
					this.loadingFlag = false;
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});
				}).catch(err => {
					console.log(err)
				});
			},

			handleChooseImage() {
				var that = this;
				if (this.imgSrcArr.length >= 6) {
					return;
				}
				uni.chooseImage({
					count: 6,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					camera: 'back',
					success: (res) => {
						that.imgSrcArr = [...that.imgSrcArr, ...res.tempFiles];
						console.log(that.imgSrcArr);
					},
				});
			},

			closePicture(index) {
				var images = this.imgSrcArr;
				images.splice(index, 1);
				this.imgSrcArr = images;
			},
		},
		onLoad(options) {
			this.userId = uni.getStorageSync('sys_user') ? uni.getStorageSync('sys_user').userId : '';
		}
	}
</script>

<style lang="scss" scoped>
	.page-body {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center;
		height: 100%;
		padding: 0 28rpx;
	}

	.suggest_content {
		margin-top: 32rpx;
		width: calc(100% - 56rpx);
		padding: 20rpx;
		border-radius: 20rpx;
		background: rgba(70, 180, 177, 0.1);
	}

	.suggest_content textarea {
		width: 100%;
		min-height: 20vh;
		max-height: 40vh;
		height: auto;
	}

	.suggest_tool {
		width: 100%;
		margin-top: 32rpx;
	}

	.suggest_tool .title {
		display: flex;
		margin-left: 12rpx;
		font-size: 28rpx;
	}

	.suggest_tool .title .icon {
		margin-right: 24rpx;
	}

	.suggest_tool .title .icon image {
		height: 28rpx;
		width: 28rpx;
	}

	.suggest_tool .add-picture {
		display: inline-block;
		width: 190rpx;
		height: 178rpx;
		background-color: #EDEDED;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin: 12rpx;
	}

	.suggest_tool .add-picture image {
		width: 48rpx;
		height: 48rpx;
	}

	.suggest_tool .add-picture text {
		display: block;
		color: #666;
		font-size: 24rpx;
	}

	.suggest_tool .up_img_list {
		width: 100%;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.suggest_tool .up_img_item {
		display: inline-block;
		background-color: #EDEDED;
		border-radius: 16rpx;
		border-radius: 16rpx;
		width: 190rpx;
		height: 178rpx;
		display: flex;
		align-items: center;
		margin: 12rpx;
		position: relative;
		overflow: hidden;
	}

	.suggest_tool .up_img_item .content {
		width: 190rpx;
		height: 190rpx;
	}

	.suggest_tool .up_img_item .close-wrapper {
		position: absolute;
		width: 44rpx;
		height: 44rpx;
		top: -5rpx;
		right: -5rpx;
		border-radius: 30% 70%;
		display: flex;
		justify-content: center;
		align-items: center;
		background: $uni-color-primary;
		filter: alpha(opacity=50);
		opacity: 0.5;
	}

	.suggest_tool .up_img_item .close {
		width: 28rpx;
		height: 28rpx;
	}

	.form_btn_wrap {
		margin-top: 56rpx;
		display: flex;
		justify-content: center;
	}

	.form-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 320rpx;
		height: 88rpx;
		border-radius: 128rpx;
		background: #46B4B1;
		color: #FFF;
		text-align: center;
		font-size: 20px;
	}

	.form-btn .icon {
		margin-right: 20rpx;
	}
</style>